#!/usr/bin/env python3
"""
Test script to verify Alpaca API connection and basic functionality
"""

import os
import sys
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce
from config import ALPACA_API_KEY, ALPACA_SECRET_KEY

def test_alpaca_connection():
    """Test basic Alpaca API connection and functionality"""
    
    print("=== Alpaca API Connection Test ===")
    
    # Check API keys
    print(f"API Key: {'SET' if ALPACA_API_KEY and ALPACA_API_KEY != 'YOUR_API_KEY' else 'NOT SET'}")
    print(f"Secret Key: {'SET' if ALPACA_SECRET_KEY and ALPACA_SECRET_KEY != 'YOUR_SECRET_KEY' else 'NOT SET'}")
    
    if ALPACA_API_KEY == "YOUR_API_KEY" or ALPACA_SECRET_KEY == "YOUR_SECRET_KEY":
        print("\n❌ ERROR: Please set your actual Alpaca API keys in config.py or environment variables")
        print("Set ALPACA_API_KEY and ALPACA_SECRET_KEY environment variables")
        return False
    
    try:
        # Initialize trading client
        print("\n1. Initializing Alpaca Trading Client...")
        trading_client = TradingClient(ALPACA_API_KEY, ALPACA_SECRET_KEY, paper=True)
        print("✅ Trading client initialized successfully")
        
        # Test account access
        print("\n2. Testing account access...")
        account = trading_client.get_account()
        print(f"✅ Account connected: {account.account_number}")
        print(f"   Status: {account.status}")
        print(f"   Buying Power: ${float(account.buying_power):,.2f}")
        print(f"   Cash: ${float(account.cash):,.2f}")
        print(f"   Portfolio Value: ${float(account.portfolio_value):,.2f}")
        
        # Test positions
        print("\n3. Testing positions access...")
        positions = trading_client.get_all_positions()
        print(f"✅ Current positions: {len(positions)}")
        for pos in positions[:5]:  # Show first 5 positions
            print(f"   {pos.symbol}: {pos.qty} shares @ ${float(pos.avg_cost):.2f}")
        
        # Test orders
        print("\n4. Testing orders access...")
        orders = trading_client.get_orders()
        print(f"✅ Recent orders: {len(orders)}")
        for order in orders[:3]:  # Show first 3 orders
            print(f"   {order.symbol}: {order.side} {order.qty} @ {order.status}")
        
        print("\n✅ All tests passed! Alpaca connection is working properly.")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        print("\nPossible issues:")
        print("1. Invalid API keys")
        print("2. Network connection problems")
        print("3. Alpaca API service issues")
        return False

def test_order_simulation():
    """Test order creation (simulation only - no actual trades)"""
    
    print("\n=== Order Simulation Test ===")
    
    try:
        trading_client = TradingClient(ALPACA_API_KEY, ALPACA_SECRET_KEY, paper=True)
        
        # Create a test order request (but don't submit it)
        print("Creating test order request for AAPL...")
        
        market_order_data = MarketOrderRequest(
            symbol="AAPL",
            qty=1,
            side=OrderSide.BUY,
            time_in_force=TimeInForce.DAY
        )
        
        print("✅ Order request created successfully")
        print(f"   Symbol: {market_order_data.symbol}")
        print(f"   Quantity: {market_order_data.qty}")
        print(f"   Side: {market_order_data.side}")
        print(f"   Time in Force: {market_order_data.time_in_force}")
        
        print("\n⚠️  Note: This is a simulation. No actual order was placed.")
        print("   To place real orders, uncomment the submit_order line in the code.")
        
        # Uncomment the line below to actually place an order (BE CAREFUL!)
        # order = trading_client.submit_order(order_data=market_order_data)
        # print(f"Order placed: {order.id}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR in order simulation: {e}")
        return False

if __name__ == "__main__":
    print("Starting Alpaca API tests...\n")
    
    # Test connection
    connection_ok = test_alpaca_connection()
    
    if connection_ok:
        # Test order simulation
        test_order_simulation()
        
        print("\n🎉 All tests completed successfully!")
        print("\nYour trading app should now be able to connect to Alpaca and place orders.")
    else:
        print("\n❌ Connection test failed. Please fix the issues above before running your trading app.")
        sys.exit(1)
