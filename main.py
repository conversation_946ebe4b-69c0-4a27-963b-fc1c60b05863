# main.py — Fixed Trading Application for alpaca-py with Enhanced Fallback Logic
import os
import logging
import time
import sys
from datetime import datetime
import argparse

# Import configuration
from config import ALPACA_API_KEY, ALPACA_SECRET_KEY, Config, config

# Import components
from data_ingestor import DataIngestor
from sentiment_analyzer import NewsSentimentAnalyzer
from price_predictor import PricePredictor
from portfolio import Portfolio
from sector_performance import get_sector_performance

# Add these imports at the top of the file
# Try absolute import
from utils import get_sp500_symbols, save_sp500_symbols, format_currency, format_percentage, chunk_list, get_sp500_symbols_with_sectors
import random

# Import utils functions
import utils

# === Ensure Required Directories Exist ===
os.makedirs("logs", exist_ok=True)
os.makedirs("models", exist_ok=True)

# === Logging Setup ===
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TradingApp")

def validate_environment():
    """Validate required environment variables and configurations"""
    required_vars = {
        'ALPACA_API_KEY': ALPACA_API_KEY,
        'ALPACA_SECRET_KEY': ALPACA_SECRET_KEY
    }
    
    missing = [k for k, v in required_vars.items() if not v]
    if missing:
        logger.error(f"Missing required environment variables: {missing}")
        return False
    
    logger.info("Environment validation passed")
    logger.info(f"Paper trading mode: {Config.PAPER_TRADING}")
    return True

def calculate_technical_sentiment(market_data, symbol: str) -> float:
    """
    Calculate sentiment based on technical indicators when other methods fail
    """
    try:
        if market_data is None or market_data.empty or len(market_data) < 2:
            logger.warning(f"Insufficient data for technical sentiment calculation for {symbol}")
            return 0.0
        
        # Get recent closing prices
        closes = market_data['close'].tail(10)  # Last 10 closing prices
        if len(closes) < 2:
            return 0.0
        
        # Calculate simple momentum (price change over period)
        price_change = (closes.iloc[-1] - closes.iloc[0]) / closes.iloc[0]
        
        # Calculate simple moving average trend
        if len(closes) >= 5:
            short_ma = closes.tail(3).mean()
            long_ma = closes.tail(7).mean()
            ma_trend = (short_ma - long_ma) / long_ma if long_ma != 0 else 0
        else:
            ma_trend = 0
        
        # Combine momentum and trend
        technical_signal = (price_change * 0.7) + (ma_trend * 0.3)
        
        # Normalize to -1 to 1 range
        sentiment = max(-1.0, min(1.0, technical_signal * 5))  # Scale factor of 5
        
        logger.info(f"Technical sentiment for {symbol}: {sentiment:.3f} (momentum: {price_change:.3f}, MA trend: {ma_trend:.3f})")
        return sentiment
        
    except Exception as e:
        logger.error(f"Technical sentiment calculation failed for {symbol}: {e}")
        return 0.0

class TradingEngine:
    """Simplified trading engine that coordinates all components"""
    
    def __init__(self):
        try:
            self.data_ingestor = DataIngestor()
            self.sentiment_analyzer = NewsSentimentAnalyzer()
            self.portfolio = Portfolio()
            self.price_predictors = {}  # Cache predictors by symbol
            
            # Test portfolio connection
            account_info = self.portfolio.get_account_info()
            if account_info:
                logger.info(f"Connected to Alpaca account: {account_info.get('account_number', 'Unknown')}")
                logger.info(f"Account status: {account_info.get('status', 'Unknown')}")
                logger.info(f"Buying power: ${account_info.get('buying_power', 0):,.2f}")
            else:
                logger.warning("Could not retrieve account information")
                
        except Exception as e:
            logger.error(f"Error initializing trading engine: {e}")
            raise
        
    def get_price_predictor(self, symbol):
        """Get or create price predictor for symbol"""
        if symbol not in self.price_predictors:
            self.price_predictors[symbol] = PricePredictor(symbol)
        return self.price_predictors[symbol]
    
    def analyze_symbol(self, symbol, market_data, news_data, sector_performance=None):
        """Analyze a single symbol and return trading signals with enhanced fallback logic"""
        try:
            # Get price prediction with fallback
            price_signal = 0.0
            try:
                predictor = self.get_price_predictor(symbol)
                price_signal = predictor.predict(market_data)
                logger.debug(f"{symbol} price prediction: {price_signal:.3f}")
            except Exception as e:
                logger.warning(f"Price prediction failed for {symbol}: {e}")
                # Fallback to technical sentiment
                price_signal = calculate_technical_sentiment(market_data, symbol)
                logger.info(f"{symbol} using technical fallback for price signal: {price_signal:.3f}")
            
            # Get sentiment analysis (already has internal fallbacks)
            headlines = news_data.get(symbol, [])
            sentiment_score = self.sentiment_analyzer.analyze_headlines(headlines)
            
            # Get sector performance boost/penalty
            sector_score = 0.0
            if sector_performance:
                # You'd need to map symbols to sectors - this is a simplified example
                sector_map = {
                    'AAPL': 'Technology',
                    'MSFT': 'Technology', 
                    'GOOGL': 'Technology',
                    'AMZN': 'Consumer Discretionary',
                    'TSLA': 'Consumer Discretionary'
                }
                sector = sector_map.get(symbol, 'Technology')
                sector_score = sector_performance.get(sector, 0.0) / 100.0  # Normalize percentage
            
            # Check if news data is reliable
            news_is_reliable = self.sentiment_analyzer.is_news_data_reliable()
            
            # Calculate combined signal with weights
            # If news data is unreliable, ignore sentiment and increase weight of other factors
            if news_is_reliable:
                combined_signal = (
                    price_signal * 0.5 +      # 50% price/technical
                    sentiment_score * 0.35 +  # 35% sentiment  
                    sector_score * 0.15       # 15% sector performance
                )
                logger.info(f"{symbol}: Using news sentiment in decision")
            else:
                combined_signal = (
                    price_signal * 0.8 +      # 80% price/technical
                    sector_score * 0.2        # 20% sector performance
                )
                logger.info(f"{symbol}: Disregarding news sentiment due to unreliable data")
            
            # Get recommendation
            recommendation = self._get_recommendation(combined_signal)
            
            logger.info(f"{symbol}: {recommendation} (Combined: {combined_signal:.3f}, "
                       f"Price: {price_signal:.3f}, Sentiment: {sentiment_score:.3f}, "
                       f"Sector: {sector_score:.3f}, News reliable: {news_is_reliable})")
            
            return {
                'symbol': symbol,
                'price_signal': price_signal,
                'sentiment_score': sentiment_score,
                'sector_score': sector_score,
                'combined_signal': combined_signal,
                'recommendation': recommendation,
                'news_reliable': news_is_reliable
            }
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            # Final fallback - use sector performance only if available
            if sector_performance:
                sector_map = {
                    'AAPL': 'Technology',
                    'MSFT': 'Technology', 
                    'GOOGL': 'Technology',
                    'AMZN': 'Consumer Discretionary',
                    'TSLA': 'Consumer Discretionary'
                }
                sector = sector_map.get(symbol, 'Technology')
                sector_score = sector_performance.get(sector, 0.0) / 100.0
                
                if sector_score > 0.02:  # 2% threshold
                    recommendation = "BUY"
                elif sector_score < -0.02:
                    recommendation = "SELL"
                else:
                    recommendation = "HOLD"
                
                logger.info(f"{symbol}: {recommendation} (Sector-only fallback: {sector_score:.3f})")
                
                return {
                    'symbol': symbol,
                    'price_signal': 0.0,
                    'sentiment_score': 0.0,
                    'sector_score': sector_score,
                    'combined_signal': sector_score,
                    'recommendation': recommendation
                }
            
            return None
    
    def _get_recommendation(self, signal):
        """Convert signal to trading recommendation"""
        if signal > Config.BUY_THRESHOLD:
            return 'BUY'
        elif signal < Config.SELL_THRESHOLD:
            return 'SELL'
        else:
            return 'HOLD'
    
    def execute_trades(self, results):
        """Execute trades based on analysis results"""
        executed_trades = []
        
        try:
            account_info = self.portfolio.get_account_info()
            buying_power = account_info.get('buying_power', 0)
            
            if buying_power < 100:  # Minimum buying power check
                logger.warning(f"Insufficient buying power: ${buying_power:,.2f}")
                return executed_trades
            
            for result in results:
                symbol = result['symbol']
                recommendation = result['recommendation']
                signal_strength = abs(result['combined_signal'])
                
                try:
                    current_position = self.portfolio.get_position(symbol)
                    
                    if recommendation == 'BUY' and signal_strength > 0.02:  # Much lower threshold
                        # Calculate position size (max 5% of buying power)
                        position_value = min(buying_power * 0.05, 1000)  # Max $1000 per position

                        # Get current stock price
                        try:
                            latest_price = self.portfolio.get_latest_price(symbol)
                            if latest_price and latest_price > 0:
                                qty = max(1, int(position_value / latest_price))
                            else:
                                qty = 1  # Fallback to 1 share
                        except:
                            qty = 1  # Fallback to 1 share if price fetch fails

                        if not current_position or float(current_position.get('qty', 0)) <= 0:
                            order = self.portfolio.place_market_order(symbol, qty, 'BUY')
                            if order:
                                executed_trades.append(order)
                                logger.info(f"BUY order executed: {qty} shares of {symbol} (signal: {signal_strength:.3f})")
                    
                    elif recommendation == 'SELL' and current_position:
                        current_qty = float(current_position.get('qty', 0))
                        if current_qty > 0:
                            # Sell half the position or all if signal is very strong
                            sell_qty = current_qty if signal_strength > 0.8 else max(1, int(current_qty / 2))

                            order = self.portfolio.place_market_order(symbol, sell_qty, 'SELL')
                            if order:
                                executed_trades.append(order)
                                logger.info(f"SELL order executed: {sell_qty} shares of {symbol} (signal: {signal_strength:.3f})")
                        else:
                            logger.info(f"No position to sell for {symbol}")
                    elif recommendation == 'SELL' and not current_position:
                        logger.info(f"SELL signal for {symbol} but no position exists")
                                
                except Exception as e:
                    logger.error(f"Error executing trade for {symbol}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error in trade execution: {e}")
        
        return executed_trades
    
    def run_trading_cycle(self, symbols, market_data=None, news_data=None, sector_data=None):
        """Run a complete trading cycle for the given symbols"""
        results = []
        
        try:
            # Fetch market data if not provided
            if market_data is None:
                logger.info("Fetching market data...")
                market_data = self.data_ingestor.get_market_data(symbols)
            
            # Fetch news data if not provided
            if news_data is None:
                logger.info("Fetching news data...")
                news_data = self.data_ingestor.get_news_data(symbols)
            
            # Get sector performance if not provided
            if sector_data is None:
                logger.info("Fetching sector data...")
                try:
                    sector_data = get_sector_performance()
                    logger.info(f"Sector performance: {sector_data}")
                except Exception as e:
                    logger.warning(f"Could not fetch sector data: {e}")
                    sector_data = {}
            
            # Validate symbols first
            if not market_data:
                logger.warning("No market data available for any symbols. Retrying with validation...")
                # Try to validate and fetch data again
                valid_symbols = self.data_ingestor.validate_symbols(symbols)
                if valid_symbols:
                    logger.info(f"Validated symbols: {valid_symbols}")
                    market_data = self.data_ingestor.get_market_data(valid_symbols, period="1mo")
                
            # Analyze each symbol
            for symbol in symbols:
                if symbol in market_data and not market_data[symbol].empty:
                    result = self.analyze_symbol(symbol, market_data[symbol], news_data, sector_data)
                    if result:
                        results.append(result)
                else:
                    logger.warning(f"No market data available for {symbol}")
                    # Try individual fetch as fallback
                    try:
                        individual_data = self.data_ingestor.get_market_data([symbol], period="1mo")
                        if individual_data and symbol in individual_data:
                            result = self.analyze_symbol(symbol, individual_data[symbol], news_data, sector_data)
                            if result:
                                results.append(result)
                        else:
                            # Final fallback with sector data only
                            result = self.analyze_symbol(symbol, None, news_data, sector_data)
                            if result:
                                results.append(result)
                    except Exception as e:
                        logger.error(f"Error analyzing {symbol}: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")
            return []

class TradingApp:
    def __init__(self):
        # Initialize all components
        self.data_ingestor = DataIngestor()
        self.sentiment_analyzer = NewsSentimentAnalyzer()
        self.portfolio = Portfolio()
        self.trading_engine = TradingEngine()
        
        # Get account info
        account_info = self.portfolio.get_account_info()
        if account_info:
            logger.info(f"Connected to Alpaca account: {account_info.get('account_number', 'Unknown')}")
            logger.info(f"Account status: {account_info.get('status', 'Unknown')}")
            logger.info(f"Buying power: ${account_info.get('buying_power', 0):,.2f}")
        
        logger.info("Trading engine initialized successfully")
    
    def get_trading_symbols(self):
        """
        Get the list of symbols to trade based on configuration
        """
        # Log the symbol source being used
        logger.info(f"Using symbol source: {Config.SYMBOL_SOURCE}")
        
        if Config.SYMBOL_SOURCE == "CUSTOM":
            logger.info(f"Using {len(Config.CUSTOM_SYMBOLS)} custom symbols: {', '.join(Config.CUSTOM_SYMBOLS[:5])}...")
            return Config.CUSTOM_SYMBOLS[:Config.MAX_SYMBOLS]
        
        elif Config.SYMBOL_SOURCE in ["SP500", "SP500_TOP_50"]:
            # Get all S&P 500 symbols
            all_sp500 = get_sp500_symbols()
            
            if not all_sp500:
                logger.warning("Failed to get S&P 500 symbols, falling back to custom symbols")
                return Config.CUSTOM_SYMBOLS[:Config.MAX_SYMBOLS]
            
            # Save for future use
            save_sp500_symbols(all_sp500)
            
            # Apply filtering and sorting logic
            sorted_symbols = all_sp500
            
            # Take top N symbols based on configuration
            if Config.SYMBOL_SOURCE == "SP500_TOP_50":
                selected_symbols = sorted_symbols[:50]
                logger.info(f"Selected top 50 S&P 500 symbols")
            else:
                selected_symbols = sorted_symbols
                logger.info(f"Using all S&P 500 symbols: {len(selected_symbols)}")
            
            # Apply maximum limit
            final_symbols = selected_symbols[:Config.MAX_SYMBOLS]
            
            logger.info(f"Trading with {len(final_symbols)} S&P 500 symbols: {', '.join(final_symbols[:5])}...")
            return final_symbols
        
        # Default fallback
        logger.warning(f"Unknown symbol source: {Config.SYMBOL_SOURCE}, using custom symbols")
        return Config.CUSTOM_SYMBOLS[:Config.MAX_SYMBOLS]
    
    def run(self):
        """Main trading loop"""
        cycle = 0
        
        while True:
            cycle += 1
            try:
                # Get symbols to trade for this cycle
                symbols = self.get_trading_symbols()
                if not symbols:
                    logger.error("No symbols to trade. Check your configuration.")
                    time.sleep(60)
                    continue
                
                logger.info(f"Starting trading cycle #{cycle} with {len(symbols)} symbols")
                logger.info(f"=== TRADING CYCLE START - {datetime.now()} ===")
                
                # Fetch market data
                logger.info("Fetching market data...")
                market_data = self.data_ingestor.get_market_data(symbols)
                if not market_data:
                    logger.error("Failed to fetch market data for any symbols")
                    time.sleep(60)
                    continue
                
                # Fetch news data
                logger.info("Fetching news data...")
                news_data = self.data_ingestor.get_news_data(symbols)
                
                # Get sector performance
                logger.info("Fetching sector data...")
                try:
                    sector_data = get_sector_performance()
                    logger.info(f"Sector performance: {sector_data}")
                except Exception as e:
                    logger.error(f"Error fetching sector data: {e}")
                    sector_data = {}
                
                # Run trading cycle
                results = self.trading_engine.run_trading_cycle(symbols, market_data, news_data, sector_data)

                # Execute trades based on results
                if results:
                    logger.info("Executing trades...")
                    executed_trades = self.trading_engine.execute_trades(results)
                    logger.info(f"Executed {len(executed_trades)} trades")
                else:
                    logger.warning("No trading results to execute")

                # Log results
                buy_count = sum(1 for r in results if r.get('recommendation') == 'BUY')
                sell_count = sum(1 for r in results if r.get('recommendation') == 'SELL')
                hold_count = sum(1 for r in results if r.get('recommendation') == 'HOLD')

                logger.info(f"Cycle #{cycle} complete: {buy_count} BUY, {sell_count} SELL, {hold_count} HOLD signals")
                
                # Sleep until next cycle
                logger.info(f"Sleeping for {Config.MARKET_DATA_INTERVAL} seconds...")
                time.sleep(Config.MARKET_DATA_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in trading cycle: {e}", exc_info=True)
                time.sleep(60)  # Sleep and retry on error

def main():
    parser = argparse.ArgumentParser(description='Stock Trading Application')
    parser.add_argument('--symbols', choices=['custom', 'sp500', 'sp500_top_50'], 
                        help='Symbol source to use for trading')
    args = parser.parse_args()
    
    # Override config if specified in command line
    if args.symbols:
        if args.symbols == 'custom':
            Config.SYMBOL_SOURCE = "CUSTOM"
        elif args.symbols == 'sp500':
            Config.SYMBOL_SOURCE = "SP500"
        elif args.symbols == 'sp500_top_50':
            Config.SYMBOL_SOURCE = "SP500_TOP_50"
    
    logger.info(f"Using symbol source: {Config.SYMBOL_SOURCE}")
    
    # Validate environment
    if not validate_environment():
        return 1

    try:
        # Run the trading application
        app = TradingApp()
        return app.run()
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        return 1

def debug_symbol_selection():
    """Debug function to test symbol selection"""
    print(f"Symbol source in config: {Config.SYMBOL_SOURCE}")
    
    # Try to get SP500 symbols directly
    sp500_symbols = get_sp500_symbols()
    print(f"SP500 symbols retrieved: {len(sp500_symbols)}")
    if sp500_symbols:
        print(f"First 5 SP500 symbols: {sp500_symbols[:5]}")
    
    # Create a TradingApp instance and get symbols
    app = TradingApp()
    symbols = app.get_trading_symbols()
    print(f"Symbols from TradingApp: {len(symbols)}")
    if symbols:
        print(f"First 5 symbols from TradingApp: {symbols[:5]}")

if __name__ == "__main__":
    # Reload configuration
    from config import reload_config
    reload_config()
    
    # Uncomment to debug symbol selection
    debug_symbol_selection()
    
    # Initialize and run the trading app
    app = TradingApp()
    app.run()
