Metadata-Version: 2.4
Name: asyncio-nats-client
Version: 0.11.5
Summary: NATS client for Python Asyncio
Home-page: https://github.com/nats-io/nats.py
Author: <PERSON><PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: Apache 2 License
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
License-File: LICENSE
Provides-Extra: nkeys
Requires-Dist: nkeys; extra == "nkeys"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: summary

Asyncio based Python client for NATS, a lightweight, high-performance cloud native messaging system
