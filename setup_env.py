#!/usr/bin/env python3
"""
Environment setup script for the stock trading application
"""

import os
import sys

def create_env_file():
    """Create a .env file with API key placeholders"""
    
    env_content = """# Alpaca API Configuration
# Get your API keys from: https://app.alpaca.markets/paper/dashboard/overview
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here

# News API Configuration (optional)
# Get your API key from: https://newsapi.org/
NEWSAPI_KEY=your_newsapi_key_here

# OpenAI API Configuration (optional, for sentiment analysis)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Created .env file with API key placeholders")
        print("📝 Please edit .env file and add your actual API keys")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def check_env_file():
    """Check if .env file exists and has valid keys"""
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        alpaca_key = os.getenv('ALPACA_API_KEY')
        alpaca_secret = os.getenv('ALPACA_SECRET_KEY')
        
        if not alpaca_key or alpaca_key == 'your_alpaca_api_key_here':
            print("❌ ALPACA_API_KEY not set in .env file")
            return False
            
        if not alpaca_secret or alpaca_secret == 'your_alpaca_secret_key_here':
            print("❌ ALPACA_SECRET_KEY not set in .env file")
            return False
            
        print("✅ .env file found with API keys")
        return True
        
    except ImportError:
        print("❌ python-dotenv not installed. Install with: pip install python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def install_dependencies():
    """Install required dependencies"""
    
    print("Installing required dependencies...")
    
    dependencies = [
        "alpaca-py",
        "python-dotenv",
        "yfinance",
        "pandas",
        "numpy",
        "requests",
        "transformers",
        "torch",
        "scikit-learn"
    ]
    
    for dep in dependencies:
        try:
            os.system(f"pip install {dep}")
            print(f"✅ Installed {dep}")
        except Exception as e:
            print(f"❌ Failed to install {dep}: {e}")

def main():
    """Main setup function"""
    
    print("=== Stock Trading App Environment Setup ===\n")
    
    # Check if .env file exists
    if not check_env_file():
        print("\n1. Creating .env file...")
        if create_env_file():
            print("\n📋 Next steps:")
            print("1. Edit the .env file and add your Alpaca API keys")
            print("2. Get your Alpaca API keys from: https://app.alpaca.markets/paper/dashboard/overview")
            print("3. Run this script again to verify your setup")
            print("4. Run 'python test_alpaca_connection.py' to test your connection")
        return
    
    print("2. Installing dependencies...")
    install_dependencies()
    
    print("\n3. Testing configuration...")
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # Test import of main modules
        from config import ALPACA_API_KEY, ALPACA_SECRET_KEY
        print("✅ Configuration loaded successfully")
        
        if ALPACA_API_KEY != "YOUR_API_KEY" and ALPACA_SECRET_KEY != "YOUR_SECRET_KEY":
            print("✅ API keys appear to be configured")
            print("\n🎉 Setup complete!")
            print("\nNext steps:")
            print("1. Run 'python test_alpaca_connection.py' to test your Alpaca connection")
            print("2. Run 'python main.py' to start the trading application")
        else:
            print("❌ API keys still have placeholder values")
            print("Please edit config.py or set environment variables")
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")

if __name__ == "__main__":
    main()
