2025-06-08 00:27:09,658 - Main - INFO - === APPLICATION START ===
2025-06-08 00:27:09,658 - Main - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 00:27:09,658 - Main - INFO - Working dir: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 00:27:09,658 - Main - INFO - ALPACA_KEY: SET
2025-06-08 00:27:09,658 - Main - INFO - NEWSAPI_KEY: SET
2025-06-08 00:32:43,664 - Main - INFO - === APPLICATION START ===
2025-06-08 00:32:43,664 - Main - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 00:32:43,665 - Main - INFO - Working dir: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 00:32:43,665 - Main - INFO - ALPACA_KEY: SET
2025-06-08 00:32:43,667 - Main - INFO - NEWSAPI_KEY: SET
2025-06-08 00:35:00,999 - Main - INFO - === APPLICATION START ===
2025-06-08 00:35:00,999 - Main - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 00:35:01,000 - Main - INFO - Working dir: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 00:35:01,001 - Main - INFO - ALPACA_KEY: SET
2025-06-08 00:35:01,001 - Main - INFO - NEWSAPI_KEY: SET
2025-06-08 00:35:49,431 - Main - INFO - === APPLICATION START ===
2025-06-08 00:35:49,432 - Main - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 00:35:49,432 - Main - INFO - Working dir: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 00:35:49,433 - Main - INFO - ALPACA_KEY: SET
2025-06-08 00:35:49,433 - Main - INFO - NEWSAPI_KEY: SET
2025-06-08 00:50:47,780 - Main - INFO - === APPLICATION START ===
2025-06-08 00:50:47,781 - Main - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 00:50:47,781 - Main - INFO - Working dir: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 00:50:47,782 - Main - INFO - ALPACA_KEY: SET
2025-06-08 00:50:47,782 - Main - INFO - NEWSAPI_KEY: SET
2025-06-08 00:52:30,473 - Main - INFO - === APPLICATION START ===
2025-06-08 00:52:30,473 - Main - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 00:52:30,474 - Main - INFO - Working dir: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 00:52:30,474 - Main - INFO - ALPACA_KEY: SET
2025-06-08 00:52:30,476 - Main - INFO - NEWSAPI_KEY: SET
2025-06-08 13:13:45,262 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 13:13:45,262 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 13:13:45,262 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 13:13:45,262 - TradingApp - INFO - Environment validation passed
2025-06-08 13:13:45,262 - TradingApp - INFO - Paper trading mode: True
2025-06-08 13:13:48,436 - SentimentAnalyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 13:13:48,436 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 13:13:48,871 - portfolio - ERROR - Error fetching account info: 'TradeAccount' object has no attribute 'day_trade_count'
2025-06-08 13:13:48,871 - TradingApp - WARNING - Could not retrieve account information
2025-06-08 13:13:48,871 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 13:13:48,871 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 13:13:48,879 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 13:13:48,879 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:13:48.879405 ===
2025-06-08 13:13:48,879 - TradingApp - INFO - Fetching market data...
2025-06-08 13:13:50,440 - TradingApp - INFO - Fetching news data...
2025-06-08 13:13:53,280 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:13:54,444 - TradingApp - INFO - Sector performance: {'Technology': np.float64(10.45), 'Industrials': np.float64(8.04), 'Financials': np.float64(3.7), 'Energy': np.float64(3.55), 'Utilities': np.float64(0.54), 'Consumer Staples': np.float64(0.26), 'Healthcare': np.float64(-0.72)}
2025-06-08 13:13:54,447 - TradingApp - WARNING - No market data available for AAPL
2025-06-08 13:13:54,448 - TradingApp - WARNING - No market data available for MSFT
2025-06-08 13:13:54,449 - TradingApp - WARNING - No market data available for GOOGL
2025-06-08 13:13:54,450 - TradingApp - WARNING - No market data available for AMZN
2025-06-08 13:13:54,451 - TradingApp - WARNING - No market data available for TSLA
2025-06-08 13:13:54,722 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:13:54,722 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:13:54,725 - TradingApp - INFO - Active positions: 0
2025-06-08 13:13:54,726 - TradingApp - WARNING - Cycle #1: No valid results
2025-06-08 13:13:54,727 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:14:54,728 - TradingApp - INFO - Starting trading cycle #2
2025-06-08 13:14:54,729 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:14:54.729469 ===
2025-06-08 13:14:54,738 - TradingApp - INFO - Fetching market data...
2025-06-08 13:14:56,189 - TradingApp - INFO - Fetching news data...
2025-06-08 13:14:58,405 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:14:59,198 - TradingApp - INFO - Sector performance: {'Technology': np.float64(10.45), 'Industrials': np.float64(8.04), 'Financials': np.float64(3.7), 'Energy': np.float64(3.55), 'Utilities': np.float64(0.54), 'Consumer Staples': np.float64(0.26), 'Healthcare': np.float64(-0.72)}
2025-06-08 13:14:59,202 - TradingApp - WARNING - No market data available for AAPL
2025-06-08 13:14:59,202 - TradingApp - WARNING - No market data available for MSFT
2025-06-08 13:14:59,202 - TradingApp - WARNING - No market data available for GOOGL
2025-06-08 13:14:59,216 - TradingApp - WARNING - No market data available for AMZN
2025-06-08 13:14:59,217 - TradingApp - WARNING - No market data available for TSLA
2025-06-08 13:14:59,503 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:14:59,503 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:14:59,503 - TradingApp - INFO - Active positions: 0
2025-06-08 13:14:59,510 - TradingApp - WARNING - Cycle #2: No valid results
2025-06-08 13:14:59,512 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:19:39,093 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 13:19:39,093 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 13:19:39,101 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 13:19:39,101 - TradingApp - INFO - Environment validation passed
2025-06-08 13:19:39,102 - TradingApp - INFO - Paper trading mode: True
2025-06-08 13:19:39,102 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 13:19:39,102 - SentimentAnalyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 13:19:39,102 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 13:19:39,428 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 13:19:39,428 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 13:19:39,428 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 13:19:39,428 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 13:19:39,428 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 13:19:39,432 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 13:19:39,433 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:19:39.432545 ===
2025-06-08 13:19:39,433 - TradingApp - INFO - Fetching market data...
2025-06-08 13:19:39,433 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:19:39,888 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:19:39,888 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:19:39,978 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:19:39,978 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:19:40,050 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:19:40,050 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:19:40,111 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:19:40,111 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:19:40,244 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:19:40,244 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:19:40,244 - TradingApp - INFO - Fetching news data...
2025-06-08 13:19:40,252 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:19:40,832 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:19:40,833 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:19:41,440 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:19:41,440 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:19:41,894 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:19:41,894 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:19:42,466 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:19:42,466 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:19:42,976 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:19:42,976 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:19:43,206 - sector_performance - ERROR - Error fetching data for Technology (XLK): unsupported format string passed to Series.__format__
2025-06-08 13:19:43,396 - sector_performance - ERROR - Error fetching data for Financials (XLF): unsupported format string passed to Series.__format__
2025-06-08 13:19:43,538 - sector_performance - ERROR - Error fetching data for Healthcare (XLV): unsupported format string passed to Series.__format__
2025-06-08 13:19:43,710 - sector_performance - ERROR - Error fetching data for Energy (XLE): unsupported format string passed to Series.__format__
2025-06-08 13:19:43,850 - sector_performance - ERROR - Error fetching data for Industrials (XLI): unsupported format string passed to Series.__format__
2025-06-08 13:19:44,019 - sector_performance - ERROR - Error fetching data for Consumer Staples (XLP): unsupported format string passed to Series.__format__
2025-06-08 13:19:44,183 - sector_performance - ERROR - Error fetching data for Utilities (XLU): unsupported format string passed to Series.__format__
2025-06-08 13:19:44,186 - sector_performance - INFO - Sector performance calculated for 7 sectors
2025-06-08 13:19:44,186 - TradingApp - INFO - Sector performance: {'Technology': 0.0, 'Financials': 0.0, 'Healthcare': 0.0, 'Energy': 0.0, 'Industrials': 0.0, 'Consumer Staples': 0.0, 'Utilities': 0.0}
2025-06-08 13:19:44,186 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:19:44,186 - SentimentAnalyzer - WARNING - OpenAI sentiment analysis failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-06-08 13:19:44,186 - TradingApp - INFO - AAPL: SELL (Signal: 0.117)
2025-06-08 13:19:44,186 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:19:44,186 - SentimentAnalyzer - WARNING - OpenAI sentiment analysis failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-06-08 13:19:44,194 - TradingApp - INFO - MSFT: SELL (Signal: 0.050)
2025-06-08 13:19:44,194 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:19:44,203 - SentimentAnalyzer - WARNING - OpenAI sentiment analysis failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-06-08 13:19:44,203 - TradingApp - INFO - GOOGL: SELL (Signal: 0.133)
2025-06-08 13:19:44,203 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:19:44,203 - SentimentAnalyzer - WARNING - OpenAI sentiment analysis failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-06-08 13:19:44,203 - TradingApp - INFO - AMZN: SELL (Signal: -0.100)
2025-06-08 13:19:44,203 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:19:44,211 - SentimentAnalyzer - WARNING - OpenAI sentiment analysis failed: 

You tried to access openai.ChatCompletion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.

You can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. 

Alternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`

A detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742

2025-06-08 13:19:44,211 - TradingApp - INFO - TSLA: SELL (Signal: 0.022)
2025-06-08 13:19:44,211 - TradingApp - INFO - Executing trades...
2025-06-08 13:19:44,406 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:19:44,517 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:19:44,609 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:19:44,708 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:19:44,799 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:19:44,799 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:19:45,085 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:19:45,085 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:19:45,092 - TradingApp - INFO - Active positions: 0
2025-06-08 13:19:45,092 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:19:45,092 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:20:11,666 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 13:31:47,797 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 13:31:47,797 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 13:31:47,797 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 13:31:47,797 - TradingApp - INFO - Environment validation passed
2025-06-08 13:31:47,797 - TradingApp - INFO - Paper trading mode: True
2025-06-08 13:31:47,805 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 13:31:48,491 - sentiment_analyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 13:31:48,491 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 13:31:48,844 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 13:31:48,844 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 13:31:48,844 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 13:31:48,844 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 13:31:48,844 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 13:31:48,855 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 13:31:48,857 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:31:48.857010 ===
2025-06-08 13:31:48,857 - TradingApp - INFO - Fetching market data...
2025-06-08 13:31:48,857 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:31:49,305 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:31:49,305 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:31:49,413 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:31:49,413 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:31:49,502 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:31:49,502 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:31:49,565 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:31:49,565 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:31:49,632 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:31:49,632 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:31:49,640 - TradingApp - INFO - Fetching news data...
2025-06-08 13:31:49,640 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:31:49,933 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:31:49,941 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:31:50,129 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:31:50,137 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:31:50,351 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:31:50,351 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:31:50,962 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:31:50,962 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:31:51,398 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:31:51,407 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:31:52,716 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:31:52,716 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:31:52,716 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:31:54,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:54,459 - openai._base_client - INFO - Retrying request to /chat/completions in 0.470013 seconds
2025-06-08 13:31:55,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:55,054 - openai._base_client - INFO - Retrying request to /chat/completions in 0.782633 seconds
2025-06-08 13:31:56,301 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:56,301 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:31:56,301 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:31:56,309 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:31:56,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:56,498 - openai._base_client - INFO - Retrying request to /chat/completions in 0.391579 seconds
2025-06-08 13:31:57,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:57,489 - openai._base_client - INFO - Retrying request to /chat/completions in 0.775097 seconds
2025-06-08 13:31:58,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:58,750 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:31:58,758 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:31:58,760 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:31:58,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:58,895 - openai._base_client - INFO - Retrying request to /chat/completions in 0.473165 seconds
2025-06-08 13:31:59,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:31:59,505 - openai._base_client - INFO - Retrying request to /chat/completions in 0.769307 seconds
2025-06-08 13:32:00,481 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:00,481 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:32:00,492 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:32:00,493 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:32:00,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:00,693 - openai._base_client - INFO - Retrying request to /chat/completions in 0.481001 seconds
2025-06-08 13:32:01,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:01,310 - openai._base_client - INFO - Retrying request to /chat/completions in 0.849883 seconds
2025-06-08 13:32:02,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:02,330 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:32:02,330 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:32:02,338 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:32:02,491 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:02,499 - openai._base_client - INFO - Retrying request to /chat/completions in 0.440471 seconds
2025-06-08 13:32:03,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:03,149 - openai._base_client - INFO - Retrying request to /chat/completions in 0.908153 seconds
2025-06-08 13:32:04,270 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:32:04,270 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:32:04,278 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:32:04,278 - TradingApp - INFO - Executing trades...
2025-06-08 13:32:04,460 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:32:04,552 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:32:04,643 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:32:04,733 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:32:04,822 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:32:04,822 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:32:05,095 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:32:05,095 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:32:05,103 - TradingApp - INFO - Active positions: 0
2025-06-08 13:32:05,105 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:32:05,106 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:33:05,115 - TradingApp - INFO - Starting trading cycle #2
2025-06-08 13:33:05,115 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:33:05.115785 ===
2025-06-08 13:33:05,125 - TradingApp - INFO - Fetching market data...
2025-06-08 13:33:05,126 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:33:05,253 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:33:05,253 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:33:05,375 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:33:05,375 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:33:05,448 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:33:05,448 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:33:05,526 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:33:05,526 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:33:05,599 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:33:05,599 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:33:05,609 - TradingApp - INFO - Fetching news data...
2025-06-08 13:33:05,609 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:33:06,232 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:33:06,232 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:33:06,843 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:33:06,843 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:33:07,004 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:33:07,004 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:33:07,533 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:33:07,533 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:33:07,700 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:33:07,700 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:33:08,792 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:33:08,792 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:33:08,800 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:33:08,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:08,968 - openai._base_client - INFO - Retrying request to /chat/completions in 0.376173 seconds
2025-06-08 13:33:09,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:09,477 - openai._base_client - INFO - Retrying request to /chat/completions in 0.760729 seconds
2025-06-08 13:33:10,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:10,426 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:33:10,435 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:33:10,435 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:33:10,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:10,632 - openai._base_client - INFO - Retrying request to /chat/completions in 0.449303 seconds
2025-06-08 13:33:11,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:11,257 - openai._base_client - INFO - Retrying request to /chat/completions in 0.953349 seconds
2025-06-08 13:33:12,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:12,377 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:33:12,377 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:33:12,377 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:33:12,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:12,582 - openai._base_client - INFO - Retrying request to /chat/completions in 0.400842 seconds
2025-06-08 13:33:13,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:13,110 - openai._base_client - INFO - Retrying request to /chat/completions in 0.848082 seconds
2025-06-08 13:33:14,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:14,115 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:33:14,115 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:33:14,123 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:33:14,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:14,315 - openai._base_client - INFO - Retrying request to /chat/completions in 0.488905 seconds
2025-06-08 13:33:15,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:15,034 - openai._base_client - INFO - Retrying request to /chat/completions in 0.784549 seconds
2025-06-08 13:33:15,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:15,967 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:33:15,974 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:33:15,974 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:33:16,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:16,115 - openai._base_client - INFO - Retrying request to /chat/completions in 0.399620 seconds
2025-06-08 13:33:16,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:16,674 - openai._base_client - INFO - Retrying request to /chat/completions in 0.934066 seconds
2025-06-08 13:33:17,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:33:17,802 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:33:17,802 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:33:17,802 - TradingApp - INFO - Executing trades...
2025-06-08 13:33:17,991 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:33:18,081 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:33:18,172 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:33:18,271 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:33:18,355 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:33:18,363 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:33:18,666 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:33:18,666 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:33:18,675 - TradingApp - INFO - Active positions: 0
2025-06-08 13:33:18,675 - TradingApp - INFO - Cycle #2 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:33:18,679 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:34:18,681 - TradingApp - INFO - Starting trading cycle #3
2025-06-08 13:34:18,681 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:34:18.681941 ===
2025-06-08 13:34:18,681 - TradingApp - INFO - Fetching market data...
2025-06-08 13:34:18,681 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:34:18,791 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:34:18,791 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:34:18,915 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:34:18,915 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:34:18,993 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:34:18,994 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:34:19,067 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:34:19,067 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:34:19,145 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:34:19,146 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:34:19,150 - TradingApp - INFO - Fetching news data...
2025-06-08 13:34:19,151 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:34:19,638 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:34:19,638 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:34:20,156 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:34:20,156 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:34:20,664 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:34:20,665 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:34:21,321 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:34:21,321 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:34:21,562 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:34:21,562 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:34:22,625 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:34:22,625 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:34:22,625 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:34:22,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:22,816 - openai._base_client - INFO - Retrying request to /chat/completions in 0.380117 seconds
2025-06-08 13:34:23,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:23,329 - openai._base_client - INFO - Retrying request to /chat/completions in 0.950010 seconds
2025-06-08 13:34:24,407 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:24,407 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:34:24,414 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:34:24,415 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:34:24,541 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:24,541 - openai._base_client - INFO - Retrying request to /chat/completions in 0.496822 seconds
2025-06-08 13:34:25,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:25,316 - openai._base_client - INFO - Retrying request to /chat/completions in 0.830270 seconds
2025-06-08 13:34:26,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:26,282 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:34:26,282 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:34:26,282 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:34:26,411 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:26,411 - openai._base_client - INFO - Retrying request to /chat/completions in 0.498425 seconds
2025-06-08 13:34:27,043 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:27,043 - openai._base_client - INFO - Retrying request to /chat/completions in 0.951222 seconds
2025-06-08 13:34:28,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:28,121 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:34:28,121 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:34:28,121 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:34:28,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:28,272 - openai._base_client - INFO - Retrying request to /chat/completions in 0.419620 seconds
2025-06-08 13:34:28,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:28,868 - openai._base_client - INFO - Retrying request to /chat/completions in 0.843544 seconds
2025-06-08 13:34:29,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:29,843 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:34:29,847 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:34:29,847 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:34:29,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:29,971 - openai._base_client - INFO - Retrying request to /chat/completions in 0.379942 seconds
2025-06-08 13:34:30,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:30,469 - openai._base_client - INFO - Retrying request to /chat/completions in 0.870047 seconds
2025-06-08 13:34:31,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:34:31,485 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:34:31,488 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:34:31,488 - TradingApp - INFO - Executing trades...
2025-06-08 13:34:31,675 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:34:31,773 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:34:31,860 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:34:31,958 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:34:32,053 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:34:32,053 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:34:32,338 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:34:32,338 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:34:32,354 - TradingApp - INFO - Active positions: 0
2025-06-08 13:34:32,356 - TradingApp - INFO - Cycle #3 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:34:32,360 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:35:32,361 - TradingApp - INFO - Starting trading cycle #4
2025-06-08 13:35:32,361 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:35:32.361680 ===
2025-06-08 13:35:32,365 - TradingApp - INFO - Fetching market data...
2025-06-08 13:35:32,365 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:35:32,492 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:35:32,492 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:35:32,590 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:35:32,597 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:35:32,667 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:35:32,667 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:35:32,742 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:35:32,742 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:35:32,804 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:35:32,804 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:35:32,814 - TradingApp - INFO - Fetching news data...
2025-06-08 13:35:32,814 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:35:42,007 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:35:42,007 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:35:42,560 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:35:42,560 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:35:42,977 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:35:42,977 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:35:43,532 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:35:43,532 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:35:44,034 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:35:44,034 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:35:45,194 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:35:45,194 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:35:45,194 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:35:45,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:45,463 - openai._base_client - INFO - Retrying request to /chat/completions in 0.393156 seconds
2025-06-08 13:35:46,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:46,011 - openai._base_client - INFO - Retrying request to /chat/completions in 0.817458 seconds
2025-06-08 13:35:47,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:47,107 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:35:47,107 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:35:47,107 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:35:47,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:47,310 - openai._base_client - INFO - Retrying request to /chat/completions in 0.465875 seconds
2025-06-08 13:35:47,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:47,926 - openai._base_client - INFO - Retrying request to /chat/completions in 0.938976 seconds
2025-06-08 13:35:49,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:49,124 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:35:49,131 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:35:49,132 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:35:49,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:49,258 - openai._base_client - INFO - Retrying request to /chat/completions in 0.460777 seconds
2025-06-08 13:35:49,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:49,869 - openai._base_client - INFO - Retrying request to /chat/completions in 0.750263 seconds
2025-06-08 13:35:50,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:50,752 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:35:50,760 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:35:50,760 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:35:50,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:50,919 - openai._base_client - INFO - Retrying request to /chat/completions in 0.453185 seconds
2025-06-08 13:35:51,611 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:51,611 - openai._base_client - INFO - Retrying request to /chat/completions in 0.836862 seconds
2025-06-08 13:35:52,633 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:52,633 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:35:52,633 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:35:52,642 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:35:52,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:52,822 - openai._base_client - INFO - Retrying request to /chat/completions in 0.391390 seconds
2025-06-08 13:35:53,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:53,347 - openai._base_client - INFO - Retrying request to /chat/completions in 0.879227 seconds
2025-06-08 13:35:54,478 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:35:54,480 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:35:54,480 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:35:54,494 - TradingApp - INFO - Executing trades...
2025-06-08 13:35:54,860 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:35:54,958 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:35:55,050 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:35:55,141 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:35:55,231 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:35:55,239 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:35:55,526 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:35:55,526 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:35:55,526 - TradingApp - INFO - Active positions: 0
2025-06-08 13:35:55,526 - TradingApp - INFO - Cycle #4 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:35:55,534 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:36:55,537 - TradingApp - INFO - Starting trading cycle #5
2025-06-08 13:36:55,537 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:36:55.537011 ===
2025-06-08 13:36:55,537 - TradingApp - INFO - Fetching market data...
2025-06-08 13:36:55,537 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:36:56,037 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:36:56,037 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:36:56,147 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:36:56,149 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:36:56,241 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:36:56,241 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:36:56,437 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:36:56,444 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:36:56,512 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:36:56,512 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:36:56,520 - TradingApp - INFO - Fetching news data...
2025-06-08 13:36:56,522 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:36:57,180 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:36:57,180 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:36:57,787 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:36:57,787 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:36:58,090 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:36:58,091 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:36:58,457 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:36:58,457 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:36:58,701 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:36:58,701 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:37:00,093 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:37:00,093 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:37:00,105 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:37:00,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:00,469 - openai._base_client - INFO - Retrying request to /chat/completions in 0.435780 seconds
2025-06-08 13:37:01,057 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:01,057 - openai._base_client - INFO - Retrying request to /chat/completions in 0.904090 seconds
2025-06-08 13:37:02,144 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:02,144 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:37:02,144 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:37:02,144 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:37:02,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:02,269 - openai._base_client - INFO - Retrying request to /chat/completions in 0.499937 seconds
2025-06-08 13:37:02,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:02,957 - openai._base_client - INFO - Retrying request to /chat/completions in 0.908628 seconds
2025-06-08 13:37:04,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:04,005 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:37:04,005 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:37:04,005 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:37:04,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:04,159 - openai._base_client - INFO - Retrying request to /chat/completions in 0.469791 seconds
2025-06-08 13:37:04,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:04,785 - openai._base_client - INFO - Retrying request to /chat/completions in 0.845293 seconds
2025-06-08 13:37:05,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:05,792 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:37:05,792 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:37:05,792 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:37:06,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:06,058 - openai._base_client - INFO - Retrying request to /chat/completions in 0.447478 seconds
2025-06-08 13:37:06,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:06,664 - openai._base_client - INFO - Retrying request to /chat/completions in 0.981026 seconds
2025-06-08 13:37:07,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:07,955 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:37:07,955 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:37:07,959 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:37:08,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:08,106 - openai._base_client - INFO - Retrying request to /chat/completions in 0.445210 seconds
2025-06-08 13:37:08,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:08,698 - openai._base_client - INFO - Retrying request to /chat/completions in 0.992538 seconds
2025-06-08 13:37:09,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:37:09,843 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:37:09,843 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:37:09,843 - TradingApp - INFO - Executing trades...
2025-06-08 13:37:10,104 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:37:10,196 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:37:10,296 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:37:10,399 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:37:10,495 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:37:10,495 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:37:10,779 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:37:10,779 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:37:10,779 - TradingApp - INFO - Active positions: 0
2025-06-08 13:37:10,788 - TradingApp - INFO - Cycle #5 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:37:10,788 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:38:10,791 - TradingApp - INFO - Starting trading cycle #6
2025-06-08 13:38:10,792 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:38:10.792541 ===
2025-06-08 13:38:10,793 - TradingApp - INFO - Fetching market data...
2025-06-08 13:38:10,793 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:38:10,913 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:38:10,913 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:38:11,014 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:38:11,014 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:38:11,090 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:38:11,093 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:38:11,175 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:38:11,175 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:38:11,242 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:38:11,242 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:38:11,250 - TradingApp - INFO - Fetching news data...
2025-06-08 13:38:11,251 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:38:11,515 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:38:11,515 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:38:12,056 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:38:12,056 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:38:12,510 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:38:12,510 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:38:13,041 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:38:13,041 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:38:13,233 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:38:13,233 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:38:14,433 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:38:14,433 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:38:14,437 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:38:14,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:14,776 - openai._base_client - INFO - Retrying request to /chat/completions in 0.391349 seconds
2025-06-08 13:38:15,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:15,295 - openai._base_client - INFO - Retrying request to /chat/completions in 0.962601 seconds
2025-06-08 13:38:16,454 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:16,456 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:38:16,459 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:38:16,460 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:38:16,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:16,592 - openai._base_client - INFO - Retrying request to /chat/completions in 0.394748 seconds
2025-06-08 13:38:17,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:17,229 - openai._base_client - INFO - Retrying request to /chat/completions in 0.846553 seconds
2025-06-08 13:38:18,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:18,353 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:38:18,353 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:38:18,363 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:38:18,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:18,479 - openai._base_client - INFO - Retrying request to /chat/completions in 0.497277 seconds
2025-06-08 13:38:19,192 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:19,193 - openai._base_client - INFO - Retrying request to /chat/completions in 0.801026 seconds
2025-06-08 13:38:20,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:20,201 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:38:20,212 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:38:20,217 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:38:20,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:20,339 - openai._base_client - INFO - Retrying request to /chat/completions in 0.481653 seconds
2025-06-08 13:38:20,969 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:20,971 - openai._base_client - INFO - Retrying request to /chat/completions in 0.901632 seconds
2025-06-08 13:38:22,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:22,044 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:38:22,055 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:38:22,055 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:38:22,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:22,247 - openai._base_client - INFO - Retrying request to /chat/completions in 0.498820 seconds
2025-06-08 13:38:22,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:22,894 - openai._base_client - INFO - Retrying request to /chat/completions in 0.840121 seconds
2025-06-08 13:38:23,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:38:23,887 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:38:23,901 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:38:23,914 - TradingApp - INFO - Executing trades...
2025-06-08 13:38:24,113 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:38:24,208 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:38:24,322 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:38:24,420 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:38:24,509 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:38:24,516 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:38:24,914 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:38:24,915 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:38:24,924 - TradingApp - INFO - Active positions: 0
2025-06-08 13:38:24,935 - TradingApp - INFO - Cycle #6 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:38:24,942 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:39:24,943 - TradingApp - INFO - Starting trading cycle #7
2025-06-08 13:39:24,943 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:39:24.943551 ===
2025-06-08 13:39:24,943 - TradingApp - INFO - Fetching market data...
2025-06-08 13:39:24,943 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:39:25,057 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:39:25,057 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:39:25,170 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:39:25,170 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:39:25,252 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:39:25,252 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:39:25,326 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:39:25,326 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:39:25,399 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:39:25,407 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:39:25,407 - TradingApp - INFO - Fetching news data...
2025-06-08 13:39:25,407 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:39:25,940 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:39:25,940 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:39:26,354 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:39:26,354 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:39:26,864 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:39:26,864 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:39:27,110 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:39:27,110 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:39:27,548 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:39:27,548 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:39:28,729 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:39:28,729 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:39:28,743 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:39:29,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:29,007 - openai._base_client - INFO - Retrying request to /chat/completions in 0.396265 seconds
2025-06-08 13:39:29,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:29,583 - openai._base_client - INFO - Retrying request to /chat/completions in 0.954812 seconds
2025-06-08 13:39:30,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:30,777 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:39:30,785 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:39:30,786 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:39:30,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:30,962 - openai._base_client - INFO - Retrying request to /chat/completions in 0.477519 seconds
2025-06-08 13:39:31,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:31,580 - openai._base_client - INFO - Retrying request to /chat/completions in 0.825329 seconds
2025-06-08 13:39:32,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:32,547 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:39:32,547 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:39:32,547 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:39:32,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:32,684 - openai._base_client - INFO - Retrying request to /chat/completions in 0.433920 seconds
2025-06-08 13:39:33,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:33,275 - openai._base_client - INFO - Retrying request to /chat/completions in 0.966737 seconds
2025-06-08 13:39:34,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:34,437 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:39:34,445 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:39:34,445 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:39:34,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:34,638 - openai._base_client - INFO - Retrying request to /chat/completions in 0.487068 seconds
2025-06-08 13:39:35,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:35,359 - openai._base_client - INFO - Retrying request to /chat/completions in 0.932349 seconds
2025-06-08 13:39:36,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:36,422 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:39:36,431 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:39:36,431 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:39:36,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:36,693 - openai._base_client - INFO - Retrying request to /chat/completions in 0.498618 seconds
2025-06-08 13:39:37,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:37,370 - openai._base_client - INFO - Retrying request to /chat/completions in 0.921615 seconds
2025-06-08 13:39:38,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:39:38,514 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:39:38,523 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:39:38,524 - TradingApp - INFO - Executing trades...
2025-06-08 13:39:38,752 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:39:38,849 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:39:38,984 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:39:39,087 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:39:39,178 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:39:39,178 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:39:39,500 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:39:39,500 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:39:39,508 - TradingApp - INFO - Active positions: 0
2025-06-08 13:39:39,514 - TradingApp - INFO - Cycle #7 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:39:39,514 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:40:28,905 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 13:40:36,386 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 13:40:36,386 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 13:40:36,386 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 13:40:36,386 - TradingApp - INFO - Environment validation passed
2025-06-08 13:40:36,394 - TradingApp - INFO - Paper trading mode: True
2025-06-08 13:40:36,394 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 13:40:37,006 - sentiment_analyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 13:40:37,006 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 13:40:37,328 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 13:40:37,328 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 13:40:37,328 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 13:40:37,328 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 13:40:37,328 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 13:40:37,337 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 13:40:37,337 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:40:37.337281 ===
2025-06-08 13:40:37,337 - TradingApp - INFO - Fetching market data...
2025-06-08 13:40:37,337 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:40:37,823 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:40:37,823 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:40:37,927 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:40:37,927 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:40:38,081 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:40:38,081 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:40:38,154 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:40:38,154 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:40:38,233 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:40:38,233 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:40:38,233 - TradingApp - INFO - Fetching news data...
2025-06-08 13:40:38,233 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:40:38,758 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:40:38,758 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:40:39,366 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:40:39,366 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:40:39,833 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:40:39,833 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:40:40,383 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:40:40,391 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:40:40,909 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:40:40,909 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:40:42,083 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:40:42,083 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:40:42,083 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:40:43,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:43,458 - openai._base_client - INFO - Retrying request to /chat/completions in 0.397288 seconds
2025-06-08 13:40:44,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:44,121 - openai._base_client - INFO - Retrying request to /chat/completions in 0.808178 seconds
2025-06-08 13:40:46,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:46,949 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:40:46,949 - TradingApp - INFO - AAPL: SELL (Signal: 0.000)
2025-06-08 13:40:46,949 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:40:47,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:47,147 - openai._base_client - INFO - Retrying request to /chat/completions in 0.477494 seconds
2025-06-08 13:40:47,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:47,755 - openai._base_client - INFO - Retrying request to /chat/completions in 0.874177 seconds
2025-06-08 13:40:48,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:48,773 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:40:48,781 - TradingApp - INFO - MSFT: SELL (Signal: 0.000)
2025-06-08 13:40:48,782 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:40:48,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:48,990 - openai._base_client - INFO - Retrying request to /chat/completions in 0.429805 seconds
2025-06-08 13:40:49,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:49,563 - openai._base_client - INFO - Retrying request to /chat/completions in 0.885603 seconds
2025-06-08 13:40:50,627 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:50,627 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:40:50,631 - TradingApp - INFO - GOOGL: SELL (Signal: 0.000)
2025-06-08 13:40:50,633 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:40:50,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:50,937 - openai._base_client - INFO - Retrying request to /chat/completions in 0.497188 seconds
2025-06-08 13:40:51,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:51,570 - openai._base_client - INFO - Retrying request to /chat/completions in 0.931425 seconds
2025-06-08 13:40:52,647 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:52,655 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:40:52,657 - TradingApp - INFO - AMZN: SELL (Signal: 0.000)
2025-06-08 13:40:52,659 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:40:52,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:52,881 - openai._base_client - INFO - Retrying request to /chat/completions in 0.396237 seconds
2025-06-08 13:40:53,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:53,498 - openai._base_client - INFO - Retrying request to /chat/completions in 0.942712 seconds
2025-06-08 13:40:54,590 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:40:54,591 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:40:54,592 - TradingApp - INFO - TSLA: SELL (Signal: 0.000)
2025-06-08 13:40:54,593 - TradingApp - INFO - Executing trades...
2025-06-08 13:40:54,789 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:40:54,890 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:40:54,994 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:40:55,088 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:40:55,188 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:40:55,188 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:40:55,836 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:40:55,836 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:40:55,844 - TradingApp - INFO - Active positions: 0
2025-06-08 13:40:55,844 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:40:55,844 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:41:14,228 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 13:42:25,346 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 13:42:25,346 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 13:42:25,346 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 13:42:25,346 - TradingApp - INFO - Environment validation passed
2025-06-08 13:42:25,346 - TradingApp - INFO - Paper trading mode: True
2025-06-08 13:42:25,354 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 13:42:26,009 - sentiment_analyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 13:42:26,009 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 13:42:26,298 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 13:42:26,298 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 13:42:26,298 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 13:42:26,298 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 13:42:26,306 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 13:42:26,306 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 13:42:26,306 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:42:26.306861 ===
2025-06-08 13:42:26,306 - TradingApp - INFO - Fetching market data...
2025-06-08 13:42:26,306 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:42:26,712 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:42:26,712 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:42:26,801 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:42:26,801 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:42:26,875 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:42:26,875 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:42:26,934 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:42:26,934 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:42:27,024 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:42:27,024 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:42:27,032 - TradingApp - INFO - Fetching news data...
2025-06-08 13:42:27,032 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:42:27,293 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:42:27,293 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:42:27,913 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:42:27,913 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:42:28,118 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:42:28,118 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:42:28,731 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:42:28,731 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:42:29,165 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:42:29,165 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:42:30,225 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:42:30,225 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:42:30,225 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:42:31,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:42:31,189 - openai._base_client - INFO - Retrying request to /chat/completions in 0.439878 seconds
2025-06-08 13:42:31,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:42:31,791 - openai._base_client - INFO - Retrying request to /chat/completions in 0.777374 seconds
2025-06-08 13:42:32,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:42:32,727 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:42:32,728 - sentiment_analyzer - INFO - OpenAI quota exceeded, switching to fallback methods
2025-06-08 13:42:32,865 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.049
2025-06-08 13:42:32,865 - TradingApp - INFO - AAPL: SELL (Combined: 0.021, Price: 0.000, Sentiment: 0.049, Sector: 0.023)
2025-06-08 13:42:32,865 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:42:32,873 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.069
2025-06-08 13:42:32,873 - TradingApp - INFO - MSFT: SELL (Combined: 0.028, Price: 0.000, Sentiment: 0.069, Sector: 0.023)
2025-06-08 13:42:32,873 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:42:32,890 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.141
2025-06-08 13:42:32,890 - TradingApp - INFO - GOOGL: SELL (Combined: 0.053, Price: 0.000, Sentiment: 0.141, Sector: 0.023)
2025-06-08 13:42:32,890 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:42:32,898 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.062
2025-06-08 13:42:32,898 - TradingApp - INFO - AMZN: SELL (Combined: 0.020, Price: 0.000, Sentiment: 0.062, Sector: -0.010)
2025-06-08 13:42:32,898 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:42:32,906 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.158
2025-06-08 13:42:32,906 - TradingApp - INFO - TSLA: SELL (Combined: 0.054, Price: 0.000, Sentiment: 0.158, Sector: -0.010)
2025-06-08 13:42:32,906 - TradingApp - INFO - Executing trades...
2025-06-08 13:42:33,094 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:42:33,183 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:42:33,280 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:42:33,369 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:42:33,466 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:42:33,466 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:42:33,747 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:42:33,747 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:42:33,756 - TradingApp - INFO - Active positions: 0
2025-06-08 13:42:33,756 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:42:33,756 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:42:45,828 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 13:42:56,899 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 13:42:56,899 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 13:42:56,899 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 13:42:56,899 - TradingApp - INFO - Environment validation passed
2025-06-08 13:42:56,899 - TradingApp - INFO - Paper trading mode: True
2025-06-08 13:42:56,899 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 13:42:57,528 - sentiment_analyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 13:42:57,530 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 13:42:57,808 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 13:42:57,808 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 13:42:57,808 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 13:42:57,808 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 13:42:57,808 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 13:42:57,816 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 13:42:57,818 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 13:42:57.817590 ===
2025-06-08 13:42:57,818 - TradingApp - INFO - Fetching market data...
2025-06-08 13:42:57,818 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 13:42:58,157 - data_ingestor - INFO - Successfully fetched 5 days of data for AAPL
2025-06-08 13:42:58,157 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 13:42:58,258 - data_ingestor - INFO - Successfully fetched 5 days of data for MSFT
2025-06-08 13:42:58,258 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 13:42:58,332 - data_ingestor - INFO - Successfully fetched 5 days of data for GOOGL
2025-06-08 13:42:58,332 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 13:42:58,398 - data_ingestor - INFO - Successfully fetched 5 days of data for AMZN
2025-06-08 13:42:58,398 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 13:42:58,466 - data_ingestor - INFO - Successfully fetched 5 days of data for TSLA
2025-06-08 13:42:58,466 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 13:42:58,473 - TradingApp - INFO - Fetching news data...
2025-06-08 13:42:58,473 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 13:42:59,032 - data_ingestor - INFO - Fetched 32 headlines for AAPL
2025-06-08 13:42:59,032 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 13:42:59,549 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 13:42:59,549 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 13:42:59,752 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 13:42:59,760 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 13:43:00,269 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 13:43:00,269 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 13:43:00,682 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 13:43:00,682 - TradingApp - INFO - Fetching sector data...
2025-06-08 13:43:01,737 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 13:43:01,737 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 13:43:01,739 - price_predictor - WARNING - Insufficient data for AAPL
2025-06-08 13:43:02,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:43:02,726 - openai._base_client - INFO - Retrying request to /chat/completions in 0.454987 seconds
2025-06-08 13:43:03,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:43:03,343 - openai._base_client - INFO - Retrying request to /chat/completions in 0.869691 seconds
2025-06-08 13:43:04,357 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 13:43:04,365 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 13:43:04,365 - sentiment_analyzer - INFO - OpenAI quota exceeded, switching to fallback methods
2025-06-08 13:43:04,446 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.049
2025-06-08 13:43:04,446 - TradingApp - INFO - AAPL: SELL (Combined: 0.021, Price: 0.000, Sentiment: 0.049, Sector: 0.023)
2025-06-08 13:43:04,450 - price_predictor - WARNING - Insufficient data for MSFT
2025-06-08 13:43:04,458 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.069
2025-06-08 13:43:04,458 - TradingApp - INFO - MSFT: SELL (Combined: 0.028, Price: 0.000, Sentiment: 0.069, Sector: 0.023)
2025-06-08 13:43:04,458 - price_predictor - WARNING - Insufficient data for GOOGL
2025-06-08 13:43:04,467 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.141
2025-06-08 13:43:04,467 - TradingApp - INFO - GOOGL: SELL (Combined: 0.053, Price: 0.000, Sentiment: 0.141, Sector: 0.023)
2025-06-08 13:43:04,467 - price_predictor - WARNING - Insufficient data for AMZN
2025-06-08 13:43:04,475 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.062
2025-06-08 13:43:04,475 - TradingApp - INFO - AMZN: SELL (Combined: 0.020, Price: 0.000, Sentiment: 0.062, Sector: -0.010)
2025-06-08 13:43:04,475 - price_predictor - WARNING - Insufficient data for TSLA
2025-06-08 13:43:04,484 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.158
2025-06-08 13:43:04,484 - TradingApp - INFO - TSLA: SELL (Combined: 0.054, Price: 0.000, Sentiment: 0.158, Sector: -0.010)
2025-06-08 13:43:04,484 - TradingApp - INFO - Executing trades...
2025-06-08 13:43:04,676 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:43:04,765 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:43:04,866 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:43:04,956 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:43:05,045 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 13:43:05,053 - TradingApp - INFO - Executed 0 trades
2025-06-08 13:43:05,331 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 13:43:05,331 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 13:43:05,339 - TradingApp - INFO - Active positions: 0
2025-06-08 13:43:05,339 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 13:43:05,339 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 13:43:07,002 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 14:40:11,904 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 14:40:11,905 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 14:40:11,906 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 14:40:11,907 - TradingApp - INFO - Environment validation passed
2025-06-08 14:40:11,908 - TradingApp - INFO - Paper trading mode: True
2025-06-08 14:40:11,908 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 14:40:12,354 - sentiment_analyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 14:40:12,354 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 14:40:12,718 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 14:40:12,718 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 14:40:12,720 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 14:40:12,721 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 14:40:12,721 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 14:40:12,722 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 14:40:12,722 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 14:40:12.722278 ===
2025-06-08 14:40:12,723 - TradingApp - INFO - Fetching market data...
2025-06-08 14:40:12,724 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 14:40:13,193 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 14:40:13,194 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 14:40:13,278 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 14:40:13,279 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 14:40:13,342 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 14:40:13,342 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 14:40:13,390 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 14:40:13,390 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 14:40:13,436 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 14:40:13,436 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 14:40:13,438 - TradingApp - INFO - Fetching news data...
2025-06-08 14:40:13,438 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 14:40:14,045 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 14:40:14,045 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 14:40:14,564 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 14:40:14,564 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 14:40:14,992 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 14:40:14,992 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 14:40:15,559 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 14:40:15,560 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 14:40:16,029 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 14:40:16,030 - TradingApp - INFO - Fetching sector data...
2025-06-08 14:40:16,854 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 14:40:16,855 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 14:40:17,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 14:40:17,689 - openai._base_client - INFO - Retrying request to /chat/completions in 0.411803 seconds
2025-06-08 14:40:18,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 14:40:18,231 - openai._base_client - INFO - Retrying request to /chat/completions in 0.910336 seconds
2025-06-08 14:40:19,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 14:40:19,439 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 14:40:19,440 - sentiment_analyzer - INFO - OpenAI quota exceeded, switching to fallback methods
2025-06-08 14:40:19,475 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.103
2025-06-08 14:40:19,475 - TradingApp - INFO - AAPL: SELL (Combined: 0.088, Price: 0.096, Sentiment: 0.103, Sector: 0.023)
2025-06-08 14:40:19,482 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.069
2025-06-08 14:40:19,483 - TradingApp - INFO - MSFT: SELL (Combined: 0.059, Price: 0.063, Sentiment: 0.069, Sector: 0.023)
2025-06-08 14:40:19,486 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.141
2025-06-08 14:40:19,487 - TradingApp - INFO - GOOGL: SELL (Combined: 0.155, Price: 0.205, Sentiment: 0.141, Sector: 0.023)
2025-06-08 14:40:19,491 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.112
2025-06-08 14:40:19,491 - TradingApp - INFO - AMZN: SELL (Combined: 0.139, Price: 0.202, Sentiment: 0.112, Sector: -0.010)
2025-06-08 14:40:19,495 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.158
2025-06-08 14:40:19,496 - TradingApp - INFO - TSLA: SELL (Combined: -0.165, Price: -0.438, Sentiment: 0.158, Sector: -0.010)
2025-06-08 14:40:19,496 - TradingApp - INFO - Executing trades...
2025-06-08 14:40:19,683 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:40:19,775 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:40:19,866 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:40:19,959 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:40:20,050 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:40:20,050 - TradingApp - INFO - Executed 0 trades
2025-06-08 14:40:20,326 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 14:40:20,326 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 14:40:20,327 - TradingApp - INFO - Active positions: 0
2025-06-08 14:40:20,328 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 14:40:20,329 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 14:41:20,330 - TradingApp - INFO - Starting trading cycle #2
2025-06-08 14:41:20,331 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 14:41:20.330197 ===
2025-06-08 14:41:20,334 - TradingApp - INFO - Fetching market data...
2025-06-08 14:41:20,335 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 14:41:20,444 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 14:41:20,445 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 14:41:20,530 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 14:41:20,531 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 14:41:20,582 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 14:41:20,583 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 14:41:20,633 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 14:41:20,633 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 14:41:20,695 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 14:41:20,695 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 14:41:20,697 - TradingApp - INFO - Fetching news data...
2025-06-08 14:41:20,698 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 14:41:21,203 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 14:41:21,204 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 14:41:21,675 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 14:41:21,676 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 14:41:22,090 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 14:41:22,090 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 14:41:22,569 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 14:41:22,569 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 14:41:22,752 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 14:41:22,753 - TradingApp - INFO - Fetching sector data...
2025-06-08 14:41:23,647 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 14:41:23,647 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 14:41:23,652 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.103
2025-06-08 14:41:23,652 - TradingApp - INFO - AAPL: SELL (Combined: 0.088, Price: 0.096, Sentiment: 0.103, Sector: 0.023)
2025-06-08 14:41:23,656 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.069
2025-06-08 14:41:23,657 - TradingApp - INFO - MSFT: SELL (Combined: 0.059, Price: 0.063, Sentiment: 0.069, Sector: 0.023)
2025-06-08 14:41:23,661 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.141
2025-06-08 14:41:23,661 - TradingApp - INFO - GOOGL: SELL (Combined: 0.155, Price: 0.205, Sentiment: 0.141, Sector: 0.023)
2025-06-08 14:41:23,665 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.112
2025-06-08 14:41:23,665 - TradingApp - INFO - AMZN: SELL (Combined: 0.139, Price: 0.202, Sentiment: 0.112, Sector: -0.010)
2025-06-08 14:41:23,669 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.158
2025-06-08 14:41:23,670 - TradingApp - INFO - TSLA: SELL (Combined: -0.165, Price: -0.438, Sentiment: 0.158, Sector: -0.010)
2025-06-08 14:41:23,670 - TradingApp - INFO - Executing trades...
2025-06-08 14:41:23,854 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 14:51:25,999 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 14:51:26,000 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 14:51:26,002 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 14:51:26,003 - TradingApp - INFO - Environment validation passed
2025-06-08 14:51:26,003 - TradingApp - INFO - Paper trading mode: True
2025-06-08 14:51:26,004 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 14:51:26,467 - sentiment_analyzer - INFO - Sentiment analyzer initialized successfully
2025-06-08 14:51:26,468 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 14:51:26,848 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 14:51:26,848 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 14:51:26,850 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 14:51:26,851 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 14:51:26,852 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 14:51:26,852 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 14:51:26,853 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 14:51:26.853965 ===
2025-06-08 14:51:26,853 - TradingApp - INFO - Fetching market data...
2025-06-08 14:51:26,854 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 14:51:27,221 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 14:51:27,221 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 14:51:27,308 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 14:51:27,309 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 14:51:27,361 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 14:51:27,361 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 14:51:27,414 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 14:51:27,414 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 14:51:27,465 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 14:51:27,465 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 14:51:27,467 - TradingApp - INFO - Fetching news data...
2025-06-08 14:51:27,467 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 14:51:28,041 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 14:51:28,041 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 14:51:28,587 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 14:51:28,587 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 14:51:29,216 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 14:51:29,217 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 14:51:29,764 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 14:51:29,764 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 14:51:30,183 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 14:51:30,183 - TradingApp - INFO - Fetching sector data...
2025-06-08 14:51:31,057 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 14:51:31,057 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 14:51:31,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 14:51:31,660 - openai._base_client - INFO - Retrying request to /chat/completions in 0.389026 seconds
2025-06-08 14:51:32,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 14:51:32,183 - openai._base_client - INFO - Retrying request to /chat/completions in 0.813663 seconds
2025-06-08 14:51:33,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-08 14:51:33,135 - sentiment_analyzer - WARNING - OpenAI sentiment analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-08 14:51:33,137 - sentiment_analyzer - INFO - OpenAI quota exceeded, switching to fallback methods
2025-06-08 14:51:33,173 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.103
2025-06-08 14:51:33,174 - TradingApp - INFO - AAPL: SELL (Combined: 0.088, Price: 0.096, Sentiment: 0.103, Sector: 0.023)
2025-06-08 14:51:33,180 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.069
2025-06-08 14:51:33,180 - TradingApp - INFO - MSFT: SELL (Combined: 0.059, Price: 0.063, Sentiment: 0.069, Sector: 0.023)
2025-06-08 14:51:33,186 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.141
2025-06-08 14:51:33,187 - TradingApp - INFO - GOOGL: SELL (Combined: 0.155, Price: 0.205, Sentiment: 0.141, Sector: 0.023)
2025-06-08 14:51:33,190 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.112
2025-06-08 14:51:33,191 - TradingApp - INFO - AMZN: SELL (Combined: 0.139, Price: 0.202, Sentiment: 0.112, Sector: -0.010)
2025-06-08 14:51:33,194 - sentiment_analyzer - INFO - Using TextBlob fallback sentiment: 0.158
2025-06-08 14:51:33,195 - TradingApp - INFO - TSLA: SELL (Combined: -0.165, Price: -0.438, Sentiment: 0.158, Sector: -0.010)
2025-06-08 14:51:33,195 - TradingApp - INFO - Executing trades...
2025-06-08 14:51:33,374 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:51:33,464 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:51:33,552 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:51:33,642 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:51:33,734 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 14:51:33,735 - TradingApp - INFO - Executed 0 trades
2025-06-08 14:51:34,005 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 14:51:34,006 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 14:51:34,007 - TradingApp - INFO - Active positions: 0
2025-06-08 14:51:34,008 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 14:51:34,008 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:24:21,507 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 15:24:21,520 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 15:24:21,520 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 15:24:21,520 - TradingApp - INFO - Environment validation passed
2025-06-08 15:26:32,184 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 15:26:32,184 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 15:26:32,184 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 15:26:32,184 - TradingApp - INFO - Environment validation passed
2025-06-08 15:27:15,005 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 15:27:15,005 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 15:27:15,005 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 15:27:15,005 - TradingApp - INFO - Environment validation passed
2025-06-08 15:27:15,005 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:27:15,005 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:27:16,091 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-06-08 15:27:55,502 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:27:55,502 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:27:55,882 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:27:55,882 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:27:55,883 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:27:55,883 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:27:55,883 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 15:27:55,883 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 15:27:55,884 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:27:55.884138 ===
2025-06-08 15:27:55,884 - TradingApp - INFO - Fetching market data...
2025-06-08 15:27:55,884 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:27:56,309 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:27:56,309 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:27:56,414 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:27:56,415 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:27:56,470 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:27:56,470 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:27:56,519 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:27:56,519 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 15:27:56,574 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 15:27:56,574 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:27:56,574 - TradingApp - INFO - Fetching news data...
2025-06-08 15:27:56,574 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:27:56,795 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:27:56,795 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:27:57,304 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:27:57,304 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:27:57,720 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:27:57,720 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:27:58,484 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:27:58,484 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 15:27:58,900 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 15:27:58,901 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:27:59,792 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:27:59,793 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:28:00,129 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:28:00,343 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:28:00,482 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:28:00,682 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.984, Sector: -0.010)
2025-06-08 15:28:00,850 - TradingApp - INFO - TSLA: SELL (Combined: -0.568, Price: -0.438, Sentiment: -0.992, Sector: -0.010)
2025-06-08 15:28:00,850 - TradingApp - INFO - Executing trades...
2025-06-08 15:28:01,100 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:28:01,183 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:28:01,267 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:28:01,372 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:28:01,451 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:28:01,451 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:28:01,734 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:28:01,734 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:28:01,734 - TradingApp - INFO - Active positions: 0
2025-06-08 15:28:01,734 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:28:01,734 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:28:33,921 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 15:31:56,050 - TradingApp - INFO - === TRADING APPLICATION STARTUP ===
2025-06-08 15:31:56,050 - TradingApp - INFO - Python: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]
2025-06-08 15:31:56,050 - TradingApp - INFO - Working directory: C:\Users\<USER>\OneDrive\Desktop\stcoktrader
2025-06-08 15:31:56,050 - TradingApp - INFO - Environment validation passed
2025-06-08 15:31:56,050 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:31:56,050 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:31:57,278 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:31:57,278 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:31:57,554 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:31:57,570 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:31:57,570 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:31:57,570 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:31:57,570 - TradingApp - INFO - Trading symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
2025-06-08 15:31:57,570 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 15:31:57,570 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:31:57.570040 ===
2025-06-08 15:31:57,570 - TradingApp - INFO - Fetching market data...
2025-06-08 15:31:57,570 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:31:57,835 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:31:57,836 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:31:57,924 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:31:57,924 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:31:57,975 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:31:57,976 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:31:58,049 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:31:58,049 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 15:31:58,102 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 15:31:58,103 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:31:58,103 - TradingApp - INFO - Fetching news data...
2025-06-08 15:31:58,104 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:31:58,340 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:31:58,340 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:31:58,844 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:31:58,845 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:31:59,027 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:31:59,028 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:31:59,261 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:31:59,261 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 15:31:59,404 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 15:31:59,405 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:32:00,316 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:32:00,316 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:32:00,998 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:32:01,218 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:32:01,371 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:32:01,600 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.984, Sector: -0.010)
2025-06-08 15:32:01,731 - TradingApp - INFO - TSLA: SELL (Combined: -0.568, Price: -0.438, Sentiment: -0.992, Sector: -0.010)
2025-06-08 15:32:01,731 - TradingApp - INFO - Executing trades...
2025-06-08 15:32:01,922 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:32:02,011 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:32:02,102 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:32:02,184 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:32:02,268 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:32:02,268 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:32:02,565 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:32:02,565 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:32:02,565 - TradingApp - INFO - Active positions: 0
2025-06-08 15:32:02,565 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:32:02,565 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:33:02,578 - TradingApp - INFO - Starting trading cycle #2
2025-06-08 15:33:02,578 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:33:02.578494 ===
2025-06-08 15:33:02,578 - TradingApp - INFO - Fetching market data...
2025-06-08 15:33:02,579 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:33:02,676 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:33:02,676 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:33:02,752 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:33:02,753 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:33:02,799 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:33:02,799 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:33:02,844 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:33:02,844 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 15:33:02,890 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 15:33:02,890 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:33:02,890 - TradingApp - INFO - Fetching news data...
2025-06-08 15:33:02,890 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:33:03,487 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:33:03,487 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:33:04,016 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:33:04,016 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:33:04,454 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:33:04,455 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:33:04,637 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:33:04,639 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 15:33:05,047 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 15:33:05,047 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:33:06,017 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:33:06,017 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:33:06,233 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:33:06,470 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:33:06,618 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:33:06,833 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.984, Sector: -0.010)
2025-06-08 15:33:07,006 - TradingApp - INFO - TSLA: SELL (Combined: -0.568, Price: -0.438, Sentiment: -0.992, Sector: -0.010)
2025-06-08 15:33:07,007 - TradingApp - INFO - Executing trades...
2025-06-08 15:33:07,225 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:33:07,314 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:33:07,401 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:33:07,484 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:33:07,584 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:33:07,584 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:33:07,849 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:33:07,849 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:33:07,849 - TradingApp - INFO - Active positions: 0
2025-06-08 15:33:07,849 - TradingApp - INFO - Cycle #2 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:33:07,849 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:34:07,857 - TradingApp - INFO - Starting trading cycle #3
2025-06-08 15:34:07,857 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:34:07.857190 ===
2025-06-08 15:34:07,857 - TradingApp - INFO - Fetching market data...
2025-06-08 15:34:07,857 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:34:07,958 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:34:07,959 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:34:08,052 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:34:08,052 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:34:08,106 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:34:08,106 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:34:08,159 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:34:08,159 - data_ingestor - INFO - Fetching market data for TSLA
2025-06-08 15:34:08,213 - data_ingestor - INFO - Successfully fetched 60 days of data for TSLA
2025-06-08 15:34:08,213 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:34:08,214 - TradingApp - INFO - Fetching news data...
2025-06-08 15:34:08,215 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:34:08,706 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:34:08,706 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:34:09,220 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:34:09,220 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:34:09,621 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:34:09,621 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:34:10,129 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:34:10,130 - data_ingestor - INFO - Fetching news for TSLA
2025-06-08 15:34:10,540 - data_ingestor - INFO - Fetched 16 headlines for TSLA
2025-06-08 15:34:10,540 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:34:11,502 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:34:11,502 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:34:11,713 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:34:11,941 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:34:12,113 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:34:12,329 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.984, Sector: -0.010)
2025-06-08 15:34:12,509 - TradingApp - INFO - TSLA: SELL (Combined: -0.568, Price: -0.438, Sentiment: -0.992, Sector: -0.010)
2025-06-08 15:34:12,509 - TradingApp - INFO - Executing trades...
2025-06-08 15:34:12,698 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:34:12,788 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:34:12,876 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:34:12,965 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:34:13,051 - portfolio - WARNING - No position found for TSLA or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:34:13,051 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:34:13,322 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:34:13,322 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:34:13,322 - TradingApp - INFO - Active positions: 0
2025-06-08 15:34:13,322 - TradingApp - INFO - Cycle #3 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:34:13,322 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:34:34,888 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 15:38:28,681 - TradingApp - INFO - Environment validation passed
2025-06-08 15:38:28,697 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:38:28,697 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:38:29,773 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:38:29,773 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:38:30,148 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:38:30,148 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:38:30,149 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:38:30,149 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:38:30,150 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 15:38:30,150 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 15:38:30,151 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:38:30.151326 ===
2025-06-08 15:38:30,151 - TradingApp - INFO - Fetching market data...
2025-06-08 15:38:30,152 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:38:30,713 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:38:30,713 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:38:30,812 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:38:30,813 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:38:30,886 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:38:30,887 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:38:30,971 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:38:30,972 - data_ingestor - INFO - Fetching market data for META
2025-06-08 15:38:31,094 - data_ingestor - INFO - Successfully fetched 60 days of data for META
2025-06-08 15:38:31,095 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:38:31,096 - TradingApp - INFO - Fetching news data...
2025-06-08 15:38:31,096 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:38:31,724 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:38:31,724 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:38:32,270 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:38:32,270 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:38:32,698 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:38:32,698 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:38:33,249 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:38:33,249 - data_ingestor - INFO - Fetching news for META
2025-06-08 15:38:33,672 - data_ingestor - INFO - Fetched 14 headlines for META
2025-06-08 15:38:33,672 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:38:34,659 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:38:34,659 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:38:35,322 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:38:35,501 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:38:35,637 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:38:35,864 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.985, Sector: -0.010)
2025-06-08 15:38:36,026 - TradingApp - INFO - META: SELL (Combined: -0.254, Price: 0.184, Sentiment: -0.998, Sector: 0.023)
2025-06-08 15:38:36,028 - TradingApp - INFO - Executing trades...
2025-06-08 15:38:36,217 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:38:36,316 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:38:36,418 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:38:36,517 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:38:36,641 - portfolio - WARNING - No position found for META or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:38:36,641 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:38:36,951 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:38:36,951 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:38:36,951 - TradingApp - INFO - Active positions: 0
2025-06-08 15:38:36,951 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:38:36,952 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:39:33,015 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 15:39:47,458 - TradingApp - INFO - Environment validation passed
2025-06-08 15:39:47,458 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:39:47,458 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:39:48,327 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:39:48,327 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:39:48,596 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:39:48,596 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:39:48,596 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:39:48,596 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:39:49,160 - data_loader - ERROR - Error fetching S&P 500 symbols: list index out of range
2025-06-08 15:39:49,161 - TradingApp - WARNING - Failed to get S&P 500 symbols, falling back to custom symbols
2025-06-08 15:39:49,162 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 15:39:49,162 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:39:49.162743 ===
2025-06-08 15:39:49,163 - TradingApp - INFO - Fetching market data...
2025-06-08 15:39:49,163 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:39:49,464 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:39:49,464 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:39:49,554 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:39:49,555 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:39:49,613 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:39:49,613 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:39:49,669 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:39:49,669 - data_ingestor - INFO - Fetching market data for META
2025-06-08 15:39:49,720 - data_ingestor - INFO - Successfully fetched 60 days of data for META
2025-06-08 15:39:49,720 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:39:49,720 - TradingApp - INFO - Fetching news data...
2025-06-08 15:39:49,720 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:39:50,227 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:39:50,227 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:39:50,732 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:39:50,732 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:39:51,141 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:39:51,142 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:39:51,634 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:39:51,635 - data_ingestor - INFO - Fetching news for META
2025-06-08 15:39:52,059 - data_ingestor - INFO - Fetched 14 headlines for META
2025-06-08 15:39:52,060 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:39:52,950 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:39:52,951 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:39:53,207 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:39:53,386 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:39:53,551 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:39:53,722 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.985, Sector: -0.010)
2025-06-08 15:39:53,896 - TradingApp - INFO - META: SELL (Combined: -0.254, Price: 0.184, Sentiment: -0.998, Sector: 0.023)
2025-06-08 15:39:53,896 - TradingApp - INFO - Executing trades...
2025-06-08 15:39:54,078 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:39:54,174 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:39:54,262 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:39:54,347 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:39:54,427 - portfolio - WARNING - No position found for META or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:39:54,427 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:39:54,762 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:39:54,762 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:39:54,763 - TradingApp - INFO - Active positions: 0
2025-06-08 15:39:54,763 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:39:54,764 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:40:08,614 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 15:41:23,890 - TradingApp - INFO - Using symbol source: SP500
2025-06-08 15:41:23,891 - TradingApp - INFO - Environment validation passed
2025-06-08 15:41:23,891 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:41:23,891 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:41:24,716 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:41:24,716 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:41:24,996 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:41:24,996 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:41:24,997 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:41:24,997 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:41:25,591 - data_loader - ERROR - Error fetching S&P 500 symbols: list index out of range
2025-06-08 15:41:25,592 - TradingApp - WARNING - Failed to get S&P 500 symbols, falling back to custom symbols
2025-06-08 15:41:25,592 - TradingApp - INFO - Starting trading cycle #1
2025-06-08 15:41:25,593 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:41:25.593164 ===
2025-06-08 15:41:25,593 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:41:25.593164 ===
2025-06-08 15:41:25,593 - TradingApp - INFO - Fetching market data...
2025-06-08 15:41:25,594 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:41:25,902 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:41:25,902 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:41:25,993 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:41:25,993 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:41:26,047 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:41:26,048 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:41:26,129 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:41:26,129 - data_ingestor - INFO - Fetching market data for META
2025-06-08 15:41:26,186 - data_ingestor - INFO - Successfully fetched 60 days of data for META
2025-06-08 15:41:26,187 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:41:26,188 - TradingApp - INFO - Fetching news data...
2025-06-08 15:41:26,188 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:41:26,673 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:41:26,674 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:41:27,184 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:41:27,184 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:41:27,595 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:41:27,596 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:41:28,108 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:41:28,109 - data_ingestor - INFO - Fetching news for META
2025-06-08 15:41:28,528 - data_ingestor - INFO - Fetched 14 headlines for META
2025-06-08 15:41:28,528 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:41:29,483 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:41:29,484 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:41:29,719 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:41:29,920 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:41:30,098 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:41:30,281 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.985, Sector: -0.010)
2025-06-08 15:41:30,453 - TradingApp - INFO - META: SELL (Combined: -0.254, Price: 0.184, Sentiment: -0.998, Sector: 0.023)
2025-06-08 15:41:30,454 - TradingApp - INFO - Executing trades...
2025-06-08 15:41:30,635 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:41:30,724 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:41:30,814 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:41:30,905 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:41:30,995 - portfolio - WARNING - No position found for META or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:41:30,995 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:41:31,268 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:41:31,268 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:41:31,269 - TradingApp - INFO - Active positions: 0
2025-06-08 15:41:31,269 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:41:31,269 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:42:31,882 - data_loader - ERROR - Error fetching S&P 500 symbols: list index out of range
2025-06-08 15:42:31,883 - TradingApp - WARNING - Failed to get S&P 500 symbols, falling back to custom symbols
2025-06-08 15:42:31,883 - TradingApp - INFO - Starting trading cycle #2
2025-06-08 15:42:31,883 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:42:31.883457 ===
2025-06-08 15:42:31,884 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:42:31.884419 ===
2025-06-08 15:42:31,884 - TradingApp - INFO - Fetching market data...
2025-06-08 15:42:31,885 - data_ingestor - INFO - Fetching market data for AAPL
2025-06-08 15:42:31,982 - data_ingestor - INFO - Successfully fetched 60 days of data for AAPL
2025-06-08 15:42:31,983 - data_ingestor - INFO - Fetching market data for MSFT
2025-06-08 15:42:32,064 - data_ingestor - INFO - Successfully fetched 60 days of data for MSFT
2025-06-08 15:42:32,064 - data_ingestor - INFO - Fetching market data for GOOGL
2025-06-08 15:42:32,123 - data_ingestor - INFO - Successfully fetched 60 days of data for GOOGL
2025-06-08 15:42:32,125 - data_ingestor - INFO - Fetching market data for AMZN
2025-06-08 15:42:32,176 - data_ingestor - INFO - Successfully fetched 60 days of data for AMZN
2025-06-08 15:42:32,176 - data_ingestor - INFO - Fetching market data for META
2025-06-08 15:42:32,245 - data_ingestor - INFO - Successfully fetched 60 days of data for META
2025-06-08 15:42:32,245 - data_ingestor - INFO - Successfully fetched market data for 5 symbols
2025-06-08 15:42:32,245 - TradingApp - INFO - Fetching news data...
2025-06-08 15:42:32,246 - data_ingestor - INFO - Fetching news for AAPL
2025-06-08 15:42:32,400 - data_ingestor - INFO - Fetched 34 headlines for AAPL
2025-06-08 15:42:32,400 - data_ingestor - INFO - Fetching news for MSFT
2025-06-08 15:42:32,573 - data_ingestor - INFO - Fetched 22 headlines for MSFT
2025-06-08 15:42:32,573 - data_ingestor - INFO - Fetching news for GOOGL
2025-06-08 15:42:32,984 - data_ingestor - INFO - Fetched 12 headlines for GOOGL
2025-06-08 15:42:32,985 - data_ingestor - INFO - Fetching news for AMZN
2025-06-08 15:42:33,452 - data_ingestor - INFO - Fetched 40 headlines for AMZN
2025-06-08 15:42:33,452 - data_ingestor - INFO - Fetching news for META
2025-06-08 15:42:33,856 - data_ingestor - INFO - Fetched 14 headlines for META
2025-06-08 15:42:33,856 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:42:34,683 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:42:34,684 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 15:42:34,910 - TradingApp - INFO - AAPL: SELL (Combined: -0.298, Price: 0.096, Sentiment: -0.997, Sector: 0.023)
2025-06-08 15:42:35,138 - TradingApp - INFO - MSFT: SELL (Combined: -0.313, Price: 0.063, Sentiment: -0.993, Sector: 0.023)
2025-06-08 15:42:35,305 - TradingApp - INFO - GOOGL: HOLD (Combined: 0.395, Price: 0.205, Sentiment: 0.827, Sector: 0.023)
2025-06-08 15:42:35,511 - TradingApp - INFO - AMZN: SELL (Combined: -0.245, Price: 0.202, Sentiment: -0.985, Sector: -0.010)
2025-06-08 15:42:35,680 - TradingApp - INFO - META: SELL (Combined: -0.254, Price: 0.184, Sentiment: -0.998, Sector: 0.023)
2025-06-08 15:42:35,680 - TradingApp - INFO - Executing trades...
2025-06-08 15:42:35,873 - portfolio - WARNING - No position found for AAPL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:42:35,963 - portfolio - WARNING - No position found for MSFT or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:42:36,053 - portfolio - WARNING - No position found for GOOGL or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:42:36,143 - portfolio - WARNING - No position found for AMZN or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:42:36,233 - portfolio - WARNING - No position found for META or error: {"code":40410000,"message":"position does not exist"}
2025-06-08 15:42:36,234 - TradingApp - INFO - Executed 0 trades
2025-06-08 15:42:36,502 - TradingApp - INFO - Portfolio value: $100,000.00
2025-06-08 15:42:36,502 - TradingApp - INFO - Daily P&L: $0.00
2025-06-08 15:42:36,503 - TradingApp - INFO - Active positions: 0
2025-06-08 15:42:36,503 - TradingApp - INFO - Cycle #2 complete: 0 BUY, 4 SELL, 1 HOLD signals
2025-06-08 15:42:36,504 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 15:42:39,772 - TradingApp - INFO - === APPLICATION TERMINATED BY USER ===
2025-06-08 15:46:48,142 - TradingApp - INFO - Using symbol source: CUSTOM
2025-06-08 15:46:48,143 - TradingApp - INFO - Environment validation passed
2025-06-08 15:46:48,143 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:46:48,144 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:46:49,004 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:46:49,005 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:46:49,297 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:46:49,298 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:46:49,298 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:46:49,299 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:46:49,299 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 15:46:49,299 - TradingApp - INFO - Starting trading cycle #1 with 5 symbols
2025-06-08 15:46:49,300 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:46:49.300220 ===
2025-06-08 15:46:49,300 - TradingApp - INFO - Fetching market data...
2025-06-08 15:46:49,301 - TradingApp - ERROR - Error in trading cycle: 'TradingApp' object has no attribute 'data_ingestor'
2025-06-08 15:48:29,396 - TradingApp - INFO - Using symbol source: CUSTOM
2025-06-08 15:48:29,397 - TradingApp - INFO - Environment validation passed
2025-06-08 15:48:29,397 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:48:29,398 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:48:30,245 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers
2025-06-08 15:48:30,245 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:48:30,532 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:48:30,532 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:48:30,533 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:48:30,533 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:48:30,534 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 15:48:30,534 - TradingApp - INFO - Starting trading cycle #1 with 5 symbols
2025-06-08 15:48:30,535 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:48:30.535478 ===
2025-06-08 15:48:30,535 - TradingApp - INFO - Fetching market data...
2025-06-08 15:48:30,536 - TradingApp - ERROR - Error in trading cycle: 'TradingApp' object has no attribute 'data_ingestor'
2025-06-08 15:49:30,537 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 15:49:30,537 - TradingApp - INFO - Starting trading cycle #2 with 5 symbols
2025-06-08 15:49:30,539 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:49:30.538404 ===
2025-06-08 15:49:30,539 - TradingApp - INFO - Fetching market data...
2025-06-08 15:49:30,540 - TradingApp - ERROR - Error in trading cycle: 'TradingApp' object has no attribute 'data_ingestor'
2025-06-08 15:50:30,541 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 15:50:30,541 - TradingApp - INFO - Starting trading cycle #3 with 5 symbols
2025-06-08 15:50:30,542 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:50:30.542222 ===
2025-06-08 15:50:30,543 - TradingApp - INFO - Fetching market data...
2025-06-08 15:50:30,544 - TradingApp - ERROR - Error in trading cycle: 'TradingApp' object has no attribute 'data_ingestor'
2025-06-08 15:59:54,113 - TradingApp - INFO - Using symbol source: CUSTOM
2025-06-08 15:59:54,114 - TradingApp - INFO - Environment validation passed
2025-06-08 15:59:54,114 - TradingApp - INFO - Paper trading mode: True
2025-06-08 15:59:54,114 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:59:54,970 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers using model: distilbert/distilbert-base-uncased-finetuned-sst-2-english
2025-06-08 15:59:54,971 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:59:54,971 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 15:59:55,250 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers using model: distilbert/distilbert-base-uncased-finetuned-sst-2-english
2025-06-08 15:59:55,250 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 15:59:55,533 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:59:55,533 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:59:55,534 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:59:55,625 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 15:59:55,626 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 15:59:55,627 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 15:59:55,627 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 15:59:55,628 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 15:59:55,628 - TradingApp - INFO - Starting trading cycle #1 with 5 symbols
2025-06-08 15:59:55,629 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 15:59:55.629208 ===
2025-06-08 15:59:55,629 - TradingApp - INFO - Fetching market data...
2025-06-08 15:59:55,630 - data_ingestor - INFO - Fetching market data for 5 symbols
2025-06-08 15:59:55,630 - data_ingestor - INFO - Processing batch 1 of 1
2025-06-08 15:59:56,442 - data_ingestor - INFO - Successfully fetched market data for 5/5 symbols
2025-06-08 15:59:56,442 - TradingApp - INFO - Fetching news data...
2025-06-08 15:59:56,443 - data_ingestor - INFO - Fetching news for 5 symbols
2025-06-08 15:59:56,444 - data_ingestor - INFO - Processing news batch 1 of 1
2025-06-08 15:59:59,142 - data_ingestor - INFO - Successfully fetched news for 5/5 symbols
2025-06-08 15:59:59,142 - TradingApp - INFO - Fetching sector data...
2025-06-08 15:59:59,965 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 15:59:59,966 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 16:00:00,089 - TradingApp - INFO - AAPL: SELL (Combined: 0.051, Price: 0.096, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:00:00,151 - TradingApp - INFO - MSFT: SELL (Combined: 0.035, Price: 0.063, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:00:00,215 - TradingApp - INFO - GOOGL: SELL (Combined: 0.106, Price: 0.205, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:00:00,274 - TradingApp - INFO - AMZN: SELL (Combined: 0.099, Price: 0.202, Sentiment: 0.000, Sector: -0.010)
2025-06-08 16:00:00,332 - TradingApp - INFO - META: SELL (Combined: 0.095, Price: 0.184, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:00:00,332 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 16:00:00,333 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 16:01:00,334 - TradingApp - INFO - Using 5 custom symbols: AAPL, MSFT, GOOGL, AMZN, META...
2025-06-08 16:01:00,334 - TradingApp - INFO - Starting trading cycle #2 with 5 symbols
2025-06-08 16:01:00,334 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 16:01:00.334950 ===
2025-06-08 16:01:00,335 - TradingApp - INFO - Fetching market data...
2025-06-08 16:01:00,335 - data_ingestor - INFO - Fetching market data for 5 symbols
2025-06-08 16:01:00,335 - data_ingestor - INFO - Processing batch 1 of 1
2025-06-08 16:01:00,934 - data_ingestor - INFO - Successfully fetched market data for 5/5 symbols
2025-06-08 16:01:00,934 - TradingApp - INFO - Fetching news data...
2025-06-08 16:01:00,935 - data_ingestor - INFO - Fetching news for 5 symbols
2025-06-08 16:01:00,936 - data_ingestor - INFO - Processing news batch 1 of 1
2025-06-08 16:01:03,212 - data_ingestor - INFO - Successfully fetched news for 5/5 symbols
2025-06-08 16:01:03,212 - TradingApp - INFO - Fetching sector data...
2025-06-08 16:01:04,140 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 16:01:04,140 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 16:01:04,203 - TradingApp - INFO - AAPL: SELL (Combined: 0.051, Price: 0.096, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:04,256 - TradingApp - INFO - MSFT: SELL (Combined: 0.035, Price: 0.063, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:04,309 - TradingApp - INFO - GOOGL: SELL (Combined: 0.106, Price: 0.205, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:04,370 - TradingApp - INFO - AMZN: SELL (Combined: 0.099, Price: 0.202, Sentiment: 0.000, Sector: -0.010)
2025-06-08 16:01:04,432 - TradingApp - INFO - META: SELL (Combined: 0.095, Price: 0.184, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:04,433 - TradingApp - INFO - Cycle #2 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 16:01:04,433 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 16:01:46,296 - TradingApp - INFO - Using symbol source: SP500_TOP_50
2025-06-08 16:01:46,296 - TradingApp - INFO - Environment validation passed
2025-06-08 16:01:46,297 - TradingApp - INFO - Paper trading mode: True
2025-06-08 16:01:46,297 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 16:01:47,365 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers using model: distilbert/distilbert-base-uncased-finetuned-sst-2-english
2025-06-08 16:01:47,366 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 16:01:47,367 - data_ingestor - INFO - DataIngestor initialized
2025-06-08 16:01:47,664 - sentiment_analyzer - INFO - Sentiment analyzer initialized with Transformers using model: distilbert/distilbert-base-uncased-finetuned-sst-2-english
2025-06-08 16:01:47,664 - portfolio - INFO - Portfolio initialized with alpaca-py client
2025-06-08 16:01:47,926 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 16:01:47,926 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 16:01:47,927 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 16:01:48,014 - TradingApp - INFO - Connected to Alpaca account: PA3EJCG35JHS
2025-06-08 16:01:48,014 - TradingApp - INFO - Account status: ACTIVE
2025-06-08 16:01:48,015 - TradingApp - INFO - Buying power: $200,000.00
2025-06-08 16:01:48,016 - TradingApp - INFO - Trading engine initialized successfully
2025-06-08 16:01:48,624 - data_loader - ERROR - Error fetching S&P 500 symbols: list index out of range
2025-06-08 16:01:48,625 - TradingApp - WARNING - Failed to get S&P 500 symbols, falling back to custom symbols
2025-06-08 16:01:48,626 - TradingApp - INFO - Starting trading cycle #1 with 5 symbols
2025-06-08 16:01:48,626 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 16:01:48.626703 ===
2025-06-08 16:01:48,627 - TradingApp - INFO - Fetching market data...
2025-06-08 16:01:48,627 - data_ingestor - INFO - Fetching market data for 5 symbols
2025-06-08 16:01:48,627 - data_ingestor - INFO - Processing batch 1 of 1
2025-06-08 16:01:49,431 - data_ingestor - INFO - Successfully fetched market data for 5/5 symbols
2025-06-08 16:01:49,431 - TradingApp - INFO - Fetching news data...
2025-06-08 16:01:49,432 - data_ingestor - INFO - Fetching news for 5 symbols
2025-06-08 16:01:49,433 - data_ingestor - INFO - Processing news batch 1 of 1
2025-06-08 16:01:51,924 - data_ingestor - INFO - Successfully fetched news for 5/5 symbols
2025-06-08 16:01:51,924 - TradingApp - INFO - Fetching sector data...
2025-06-08 16:01:52,839 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 16:01:52,839 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 16:01:52,962 - TradingApp - INFO - AAPL: SELL (Combined: 0.051, Price: 0.096, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:53,035 - TradingApp - INFO - MSFT: SELL (Combined: 0.035, Price: 0.063, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:53,098 - TradingApp - INFO - GOOGL: SELL (Combined: 0.106, Price: 0.205, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:53,178 - TradingApp - INFO - AMZN: SELL (Combined: 0.099, Price: 0.202, Sentiment: 0.000, Sector: -0.010)
2025-06-08 16:01:53,283 - TradingApp - INFO - META: SELL (Combined: 0.095, Price: 0.184, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:01:53,283 - TradingApp - INFO - Cycle #1 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 16:01:53,284 - TradingApp - INFO - Sleeping for 60 seconds...
2025-06-08 16:02:53,901 - data_loader - ERROR - Error fetching S&P 500 symbols: list index out of range
2025-06-08 16:02:53,901 - TradingApp - WARNING - Failed to get S&P 500 symbols, falling back to custom symbols
2025-06-08 16:02:53,902 - TradingApp - INFO - Starting trading cycle #2 with 5 symbols
2025-06-08 16:02:53,902 - TradingApp - INFO - === TRADING CYCLE START - 2025-06-08 16:02:53.902314 ===
2025-06-08 16:02:53,903 - TradingApp - INFO - Fetching market data...
2025-06-08 16:02:53,903 - data_ingestor - INFO - Fetching market data for 5 symbols
2025-06-08 16:02:53,903 - data_ingestor - INFO - Processing batch 1 of 1
2025-06-08 16:02:54,504 - data_ingestor - INFO - Successfully fetched market data for 5/5 symbols
2025-06-08 16:02:54,505 - TradingApp - INFO - Fetching news data...
2025-06-08 16:02:54,505 - data_ingestor - INFO - Fetching news for 5 symbols
2025-06-08 16:02:54,506 - data_ingestor - INFO - Processing news batch 1 of 1
2025-06-08 16:02:57,285 - data_ingestor - INFO - Successfully fetched news for 5/5 symbols
2025-06-08 16:02:57,285 - TradingApp - INFO - Fetching sector data...
2025-06-08 16:02:58,183 - sector_performance - INFO - Sector performance calculated for 11 sectors
2025-06-08 16:02:58,184 - TradingApp - INFO - Sector performance: {'Technology': 2.32, 'Financials': 0.59, 'Healthcare': 1.25, 'Energy': 0.98, 'Industrials': 1.61, 'Consumer Staples': -1.44, 'Utilities': -1.2, 'Consumer Discretionary': -0.99, 'Materials': 1.07, 'Real Estate': 0.22, 'Communication Services': 1.78}
2025-06-08 16:02:58,242 - TradingApp - INFO - AAPL: SELL (Combined: 0.051, Price: 0.096, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:02:58,307 - TradingApp - INFO - MSFT: SELL (Combined: 0.035, Price: 0.063, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:02:58,359 - TradingApp - INFO - GOOGL: SELL (Combined: 0.106, Price: 0.205, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:02:58,417 - TradingApp - INFO - AMZN: SELL (Combined: 0.099, Price: 0.202, Sentiment: 0.000, Sector: -0.010)
2025-06-08 16:02:58,491 - TradingApp - INFO - META: SELL (Combined: 0.095, Price: 0.184, Sentiment: 0.000, Sector: 0.023)
2025-06-08 16:02:58,492 - TradingApp - INFO - Cycle #2 complete: 0 BUY, 5 SELL, 0 HOLD signals
2025-06-08 16:02:58,492 - TradingApp - INFO - Sleeping for 60 seconds...
