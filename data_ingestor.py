# data_ingestor.py - Fixed for reliable data fetching
import logging
import yfinance as yf
import pandas as pd
import time
from datetime import datetime, timedelta
import requests
from config import Config

logger = logging.getLogger(__name__)

class DataIngestor:
    def __init__(self):
        self.newsapi_key = Config.NEWSAPI_KEY
        logger.info("DataIngestor initialized")

    def get_market_data(self, symbols, period="60d"):
        """
        Fetch market data for multiple symbols
        Returns a dictionary of dataframes indexed by symbol
        
        Args:
            symbols: List of ticker symbols
            period: Time period to fetch (default: "60d")
        """
        result = {}
        logger.info(f"Fetching market data for {len(symbols)} symbols")
        
        # Process in smaller batches to avoid overwhelming APIs
        batch_size = 20
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1} of {(len(symbols) + batch_size - 1)//batch_size}")
            
            for symbol in batch:
                try:
                    logger.debug(f"Fetching market data for {symbol}")
                    df = self._fetch_market_data(symbol, period)
                    if df is not None and not df.empty:
                        result[symbol] = df
                        logger.debug(f"Successfully fetched {len(df)} days of data for {symbol}")
                    else:
                        logger.warning(f"No data returned for {symbol}")
                except Exception as e:
                    logger.error(f"Error fetching market data for {symbol}: {e}")
                
                # Small delay to avoid rate limiting
                time.sleep(0.05)
        
        logger.info(f"Successfully fetched market data for {len(result)}/{len(symbols)} symbols")
        return result

    def get_news_data(self, symbols):
        """
        Fetch news data for multiple symbols
        Returns a dictionary of news lists indexed by symbol
        """
        result = {}
        logger.info(f"Fetching news for {len(symbols)} symbols")
        
        # Process in smaller batches to avoid rate limiting
        batch_size = 10
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i+batch_size]
            logger.info(f"Processing news batch {i//batch_size + 1} of {(len(symbols) + batch_size - 1)//batch_size}")
            
            for symbol in batch:
                try:
                    logger.debug(f"Fetching news for {symbol}")
                    news = self._fetch_news(symbol)
                    if news:
                        result[symbol] = news
                        logger.debug(f"Fetched {len(news)} headlines for {symbol}")
                    else:
                        logger.warning(f"No news found for {symbol}")
                        result[symbol] = []
                except Exception as e:
                    logger.error(f"Error fetching news for {symbol}: {e}")
                    result[symbol] = []
                
                # Add a delay to avoid rate limiting
                time.sleep(0.2)
        
        logger.info(f"Successfully fetched news for {len(result)}/{len(symbols)} symbols")
        return result

    def _fetch_market_data(self, symbol, period="60d"):
        """
        Fetch market data for a single symbol
        Returns a DataFrame or None if no data is available
        
        Args:
            symbol: Ticker symbol
            period: Time period to fetch (default: "60d")
        """
        try:
            ticker = yf.Ticker(symbol)
            df = ticker.history(period=period, interval="1d")
            
            if df.empty:
                logger.warning(f"No data returned for {symbol}")
                return None
            
            df = df.reset_index()
            
            # Check for required columns with more detailed error reporting
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.warning(f"Missing required columns for {symbol}: {missing_columns}. Available: {df.columns.tolist()}")
                return None
                
            # Check for NaN values
            if df[required_columns].isna().any().any():
                logger.warning(f"Data for {symbol} contains NaN values. Attempting to clean...")
                df = df.dropna(subset=required_columns)
                if df.empty:
                    logger.warning(f"After removing NaN values, no data remains for {symbol}")
                    return None
            
            return df
        except Exception as e:
            logger.error(f"Error fetching market data for {symbol}: {e}")
            return None

    def _fetch_news(self, symbol):
        """
        Fetch news for a specific symbol
        Returns a list of news headlines
        """
        try:
            # Use company name for better news results
            company_name = self._get_company_name(symbol)
            search_term = f"{company_name} OR {symbol}"
            
            url = f"https://newsapi.org/v2/everything"
            params = {
                "q": search_term,
                "apiKey": self.newsapi_key,
                "language": "en",
                "sortBy": "publishedAt",
                "pageSize": 5,  # Reduced to avoid rate limits
                "from": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            }
            
            response = requests.get(url, params=params)
            if response.status_code == 429:
                logger.warning(f"News API rate limit exceeded for {symbol}. Waiting and retrying...")
                time.sleep(1)  # Wait a bit longer
                response = requests.get(url, params=params)
                
            if response.status_code != 200:
                logger.error(f"News API error for {symbol}: {response.status_code} - {response.text}")
                return []
                
            data = response.json()
            if data.get("status") != "ok":
                logger.error(f"News API returned error for {symbol}: {data}")
                return []
                
            articles = data.get("articles", [])
            return [article["title"] for article in articles if article.get("title")]
        except Exception as e:
            logger.error(f"Error fetching news for {symbol}: {e}")
            return []

    def _get_company_name(self, symbol):
        """Get company name for symbol (simplified mapping)"""
        company_names = {
            'AAPL': 'Apple',
            'MSFT': 'Microsoft',
            'GOOGL': 'Google Alphabet',
            'AMZN': 'Amazon',
            'TSLA': 'Tesla',
            'META': 'Meta Facebook',
            'NVDA': 'Nvidia',
            'NFLX': 'Netflix'
        }
        return company_names.get(symbol, symbol)

    def get_real_time_price(self, symbol):
        """Get real-time price for a symbol"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            
            if current_price:
                return float(current_price)
            else:
                # Fallback to recent close price
                hist = ticker.history(period="1d", interval="1m")
                if not hist.empty:
                    return float(hist['Close'].iloc[-1])
                    
        except Exception as e:
            logger.error(f"Error getting real-time price for {symbol}: {e}")
            
        return None

    def get_market_status(self):
        """Check if market is open"""
        try:
            # Simple check using SPY (S&P 500 ETF)
            spy = yf.Ticker("SPY")
            hist = spy.history(period="1d", interval="1m")
            
            if not hist.empty:
                last_update = hist.index[-1]
                now = datetime.now(last_update.tz)
                
                # If last update was within 5 minutes, market is likely open
                time_diff = (now - last_update).total_seconds() / 60
                return time_diff < 5
                
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            
        return False  # Assume closed if we can't determine

    def validate_symbols(self, symbols):
        """Validate that symbols exist and have data"""
        valid_symbols = []
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                
                # Check if we got valid info
                if info and info.get('symbol') == symbol:
                    valid_symbols.append(symbol)
                    logger.info(f"Symbol {symbol} validated")
                else:
                    logger.warning(f"Symbol {symbol} may not be valid")
                    
            except Exception as e:
                logger.error(f"Error validating symbol {symbol}: {e}")
                
        return valid_symbols
