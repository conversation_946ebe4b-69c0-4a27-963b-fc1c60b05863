import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Union
from config import Config

logger = logging.getLogger(__name__)

class LongTermTechnicalAnalyzer:
    """Technical analysis focused on longer-term trends for 2-6 month investments"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("Initializing long-term technical analyzer")
        
        # Define timeframes for analysis
        self.timeframes = Config.TECHNICAL_TIMEFRAMES
    
    def analyze_technicals(self, market_data: pd.DataFrame, symbol: str) -> Dict[str, float]:
        """
        Analyze technical indicators across multiple timeframes
        Returns a dictionary of technical scores by timeframe
        """
        if market_data is None or market_data.empty:
            self.logger.warning(f"No market data available for {symbol}")
            return {'daily': 0.0, 'weekly': 0.0, 'monthly': 0.0, 'overall': 0.0}
        
        # Resample data to different timeframes
        try:
            # Ensure data is sorted by date
            market_data = market_data.sort_index()
            
            # Create resampled dataframes
            daily_data = market_data
            weekly_data = market_data.resample('W').agg({
                'Open': 'first', 
                'High': 'max', 
                'Low': 'min', 
                'Close': 'last', 
                'Volume': 'sum'
            }).dropna()
            monthly_data = market_data.resample('M').agg({
                'Open': 'first', 
                'High': 'max', 
                'Low': 'min', 
                'Close': 'last', 
                'Volume': 'sum'
            }).dropna()
            
            # Analyze each timeframe
            daily_score = self._analyze_timeframe(daily_data, 'daily')
            weekly_score = self._analyze_timeframe(weekly_data, 'weekly')
            monthly_score = self._analyze_timeframe(monthly_data, 'monthly')
            
            # Weight the scores (more weight on longer timeframes for long-term investing)
            overall_score = (
                daily_score * 0.2 +    # 20% weight on daily
                weekly_score * 0.35 +  # 35% weight on weekly
                monthly_score * 0.45   # 45% weight on monthly
            )
            
            return {
                'daily': daily_score,
                'weekly': weekly_score,
                'monthly': monthly_score,
                'overall': overall_score
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing technicals for {symbol}: {e}")
            return {'daily': 0.0, 'weekly': 0.0, 'monthly': 0.0, 'overall': 0.0}
    
    def _analyze_timeframe(self, data: pd.DataFrame, timeframe: str) -> float:
        """
        Analyze technical indicators for a specific timeframe
        Returns a score between -1 and 1
        """
        if len(data) < 50:  # Need sufficient data for analysis
            self.logger.warning(f"Insufficient {timeframe} data for analysis: {len(data)} points")
            return 0.0
        
        try:
            # Calculate various technical indicators
            scores = []
            
            # 1. Trend indicators
            # Moving Average Crossovers (longer periods for long-term analysis)
            ma50 = data['Close'].rolling(window=50).mean()
            ma200 = data['Close'].rolling(window=200).mean()
            
            # Score based on MA crossover and position
            if len(ma50) > 0 and len(ma200) > 0:
                last_ma50 = ma50.iloc[-1]
                last_ma200 = ma200.iloc[-1]
                
                # Price relative to moving averages
                price = data['Close'].iloc[-1]
                if price > last_ma50 and price > last_ma200:
                    scores.append(0.8)  # Strong uptrend
                elif price > last_ma50 and price < last_ma200:
                    scores.append(0.3)  # Potential uptrend
                elif price < last_ma50 and price > last_ma200:
                    scores.append(-0.1)  # Mixed signals
                else:
                    scores.append(-0.6)  # Downtrend
                
                # MA crossover
                if ma50.iloc[-1] > ma200.iloc[-1] and ma50.iloc[-2] <= ma200.iloc[-2]:
                    scores.append(0.9)  # Golden cross (very bullish)
                elif ma50.iloc[-1] < ma200.iloc[-1] and ma50.iloc[-2] >= ma200.iloc[-2]:
                    scores.append(-0.9)  # Death cross (very bearish)
                elif ma50.iloc[-1] > ma200.iloc[-1]:
                    scores.append(0.5)  # Bullish alignment
                else:
                    scores.append(-0.5)  # Bearish alignment
            
            # 2. MACD (longer periods for long-term analysis)
            try:
                # Calculate MACD using pandas
                ema_fast = data['Close'].ewm(span=26, adjust=False).mean()
                ema_slow = data['Close'].ewm(span=52, adjust=False).mean()
                macd = ema_fast - ema_slow
                macd_signal = macd.ewm(span=18, adjust=False).mean()
                macd_hist = macd - macd_signal
                
                # MACD crossing signal line
                if macd.iloc[-1] > macd_signal.iloc[-1] and macd.iloc[-2] <= macd_signal.iloc[-2]:
                    scores.append(0.7)  # Bullish crossover
                elif macd.iloc[-1] < macd_signal.iloc[-1] and macd.iloc[-2] >= macd_signal.iloc[-2]:
                    scores.append(-0.7)  # Bearish crossover
                # MACD above/below signal
                elif macd.iloc[-1] > macd_signal.iloc[-1]:
                    scores.append(0.4)  # Bullish
                else:
                    scores.append(-0.4)  # Bearish
                    
                # MACD histogram trend
                if macd_hist.iloc[-1] > 0 and macd_hist.iloc[-2] > 0 and macd_hist.iloc[-1] > macd_hist.iloc[-2]:
                    scores.append(0.6)  # Strengthening bullish momentum
                elif macd_hist.iloc[-1] < 0 and macd_hist.iloc[-2] < 0 and macd_hist.iloc[-1] < macd_hist.iloc[-2]:
                    scores.append(-0.6)  # Strengthening bearish momentum
            except Exception as e:
                self.logger.warning(f"Error calculating MACD: {e}")
            
            # 3. RSI (adjusted for long-term)
            try:
                # Calculate RSI using pandas
                delta = data['Close'].diff()
                gain = delta.where(delta > 0, 0)
                loss = -delta.where(delta < 0, 0)
                
                avg_gain = gain.rolling(window=21).mean()
                avg_loss = loss.rolling(window=21).mean()
                
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                
                last_rsi = rsi.iloc[-1]
                
                # RSI interpretation
                if last_rsi > 70:
                    scores.append(-0.5)  # Overbought
                elif last_rsi < 30:
                    scores.append(0.5)  # Oversold
                elif 40 <= last_rsi <= 60:
                    scores.append(0.1)  # Neutral
                elif 60 < last_rsi < 70:
                    scores.append(-0.2)  # Approaching overbought
                elif 30 < last_rsi < 40:
                    scores.append(0.2)  # Approaching oversold
            except Exception as e:
                self.logger.warning(f"Error calculating RSI: {e}")
            
            # 4. Bollinger Bands
            try:
                # Calculate Bollinger Bands
                rolling_mean = data['Close'].rolling(window=40).mean()
                rolling_std = data['Close'].rolling(window=40).std()
                
                upper_band = rolling_mean + (rolling_std * 2)
                lower_band = rolling_mean - (rolling_std * 2)
                
                price = data['Close'].iloc[-1]
                bb_width = (upper_band.iloc[-1] - lower_band.iloc[-1]) / rolling_mean.iloc[-1]
                
                # Price position relative to bands
                if price > upper_band.iloc[-1]:
                    scores.append(-0.6)  # Overbought
                elif price < lower_band.iloc[-1]:
                    scores.append(0.6)  # Oversold
                
                # Bollinger Band width (volatility)
                if bb_width < 0.1:
                    scores.append(0.4)  # Low volatility, potential breakout
            except Exception as e:
                self.logger.warning(f"Error calculating Bollinger Bands: {e}")
            
            # 5. ADX (Trend Strength) - Simplified version
            try:
                # Calculate directional movement
                high_diff = data['High'].diff()
                low_diff = data['Low'].diff()
                
                pos_dm = high_diff.where(high_diff > 0, 0)
                neg_dm = -low_diff.where(low_diff < 0, 0)
                
                # True Range
                tr1 = data['High'] - data['Low']
                tr2 = abs(data['High'] - data['Close'].shift(1))
                tr3 = abs(data['Low'] - data['Close'].shift(1))
                tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                
                # Smoothed values
                smoothing = 14
                atr = tr.rolling(window=smoothing).mean()
                
                # Directional indicators
                pdi = 100 * (pos_dm.rolling(window=smoothing).mean() / atr)
                ndi = 100 * (neg_dm.rolling(window=smoothing).mean() / atr)
                
                # ADX calculation
                dx = 100 * abs(pdi - ndi) / (pdi + ndi)
                adx = dx.rolling(window=smoothing).mean()
                
                last_adx = adx.iloc[-1]
                
                # ADX interpretation
                if last_adx > 30:
                    # Strong trend - check if bullish or bearish
                    if pdi.iloc[-1] > ndi.iloc[-1]:
                        scores.append(0.7)  # Strong bullish trend
                    else:
                        scores.append(-0.7)  # Strong bearish trend
                elif last_adx < 20:
                    scores.append(0.0)  # No trend/ranging market
            except Exception as e:
                self.logger.warning(f"Error calculating ADX: {e}")
            
            # 6. Long-term price performance
            try:
                # Calculate returns over different periods
                returns_3m = data['Close'].pct_change(60).iloc[-1] if len(data) > 60 else 0
                returns_6m = data['Close'].pct_change(120).iloc[-1] if len(data) > 120 else 0
                returns_12m = data['Close'].pct_change(250).iloc[-1] if len(data) > 250 else 0
                
                # Score based on returns
                if returns_3m > 0.15:
                    scores.append(0.5)  # Strong 3-month performance
                elif returns_3m < -0.15:
                    scores.append(-0.5)  # Weak 3-month performance
                    
                if returns_6m > 0.25:
                    scores.append(0.6)  # Strong 6-month performance
                elif returns_6m < -0.25:
                    scores.append(-0.6)  # Weak 6-month performance
                    
                if returns_12m > 0.40:
                    scores.append(0.7)  # Strong 12-month performance
                elif returns_12m < -0.40:
                    scores.append(-0.7)  # Weak 12-month performance
            except Exception as e:
                self.logger.warning(f"Error calculating price performance: {e}")
            
            # Calculate average score
            if scores:
                avg_score = sum(scores) / len(scores)
                # Clamp between -1 and 1
                final_score = max(-1.0, min(1.0, avg_score))
                self.logger.debug(f"{timeframe} technical score: {final_score:.3f}")
                return final_score
            else:
                self.logger.warning(f"No technical scores calculated for {timeframe}")
                return 0.0
            
        except Exception as e:
            self.logger.error(f"Error in {timeframe} technical analysis: {e}")
            return 0.0








