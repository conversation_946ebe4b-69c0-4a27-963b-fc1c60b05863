# test_components.py - Test script for verifying components
import logging
import sys
from data_ingestor import DataIngestor
from sentiment_analyzer import NewsSentimentAnalyzer
from portfolio import Portfolio
from config import Config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TestComponents")

def test_data_ingestor():
    logger.info("Testing DataIngestor...")
    ingestor = DataIngestor()
    symbols = ["AAPL", "MSFT"]
    
    logger.info("Fetching market data...")
    market_data = ingestor.get_market_data(symbols)
    if market_data:
        logger.info(f"Successfully fetched market data for {len(market_data)} symbols")
        for symbol, data in market_data.items():
            logger.info(f"{symbol}: {len(data)} data points")
    else:
        logger.error("Failed to fetch market data")
    
    logger.info("Fetching news data...")
    news_data = ingestor.get_news_data(symbols)
    if news_data:
        logger.info(f"Successfully fetched news for {len(news_data)} symbols")
        for symbol, news in news_data.items():
            logger.info(f"{symbol}: {len(news)} news items")
    else:
        logger.error("Failed to fetch news data")

def test_sentiment_analyzer():
    logger.info("Testing SentimentAnalyzer...")
    analyzer = NewsSentimentAnalyzer()
    headlines = [
        "Company reports record profits",
        "Stock price plummets after earnings miss",
        "New product launch exceeds expectations"
    ]
    
    sentiment = analyzer.analyze_headlines(headlines)
    logger.info(f"Sentiment score: {sentiment}")

def test_portfolio():
    logger.info("Testing Portfolio...")
    portfolio = Portfolio()
    
    account_info = portfolio.get_account_info()
    if account_info:
        logger.info(f"Account info: {account_info}")
    else:
        logger.error("Failed to get account info")

def main():
    logger.info("=== COMPONENT TESTS ===")
    
    try:
        test_data_ingestor()
        test_sentiment_analyzer()
        test_portfolio()
        logger.info("All tests completed")
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
