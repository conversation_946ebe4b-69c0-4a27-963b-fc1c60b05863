import os

# API Keys - Load from environment variables for security
ALPACA_API_KEY = os.environ.get("ALPACA_API_KEY", "YOUR_API_KEY")  # Replace with your actual API key
ALPACA_SECRET_KEY = os.environ.get("ALPACA_SECRET_KEY", "YOUR_SECRET_KEY")  # Replace with your actual secret key
ALPACA_BASE_URL = "https://paper-api.alpaca.markets"  # Use paper trading URL
NEWSAPI_KEY = os.environ.get("NEWSAPI_KEY", "")  # Added NewsAPI key

# Trading Symbols Configuration
SYMBOL_SOURCE = "SP500"  # Options: "CUSTOM", "SP500", "SP500_TOP_50"
CUSTOM_SYMBOLS = ["AAPL", "MSFT", "GOOGL", "AMZN", "META"]
MAX_SYMBOLS = 500

# Trading Configuration
class Config:
    # Trading parameters
    PAPER_TRADING = True
    BUY_THRESHOLD = 0.05  # Lowered from 0.2 to be more sensitive
    SELL_THRESHOLD = -0.05  # Lowered from -0.2 to be more sensitive
    
    # Data parameters
    MARKET_DATA_INTERVAL = 60  # seconds
    
    # Symbol source
    SYMBOL_SOURCE = SYMBOL_SOURCE
    CUSTOM_SYMBOLS = CUSTOM_SYMBOLS
    MAX_SYMBOLS = MAX_SYMBOLS
    
    # API Keys
    NEWSAPI_KEY = NEWSAPI_KEY  # Added NewsAPI key
    
    # Sentiment analysis
    SENTIMENT_MODEL = "distilbert-base-uncased-finetuned-sst-2-english"  # Default sentiment model

# Create a config instance for backward compatibility
config = Config()

def reload_config():
    """Reload configuration"""
    global SYMBOL_SOURCE, NEWSAPI_KEY
    Config.SYMBOL_SOURCE = SYMBOL_SOURCE
    Config.NEWSAPI_KEY = NEWSAPI_KEY
    print(f"Reloaded config: SYMBOL_SOURCE={SYMBOL_SOURCE}")

# Export variables for backward compatibility
__all__ = [
    'ALPACA_API_KEY', 'ALPACA_SECRET_KEY', 'NEWSAPI_KEY', 'Config', 'config'
]

if __name__ == "__main__":
    print(f"ALPACA_API_KEY: {'SET' if ALPACA_API_KEY else 'MISSING'}")
    print(f"ALPACA_SECRET_KEY: {'SET' if ALPACA_SECRET_KEY else 'MISSING'}")
    print(f"NEWSAPI_KEY: {'SET' if NEWSAPI_KEY else 'MISSING'}")
    print(f"PAPER TRADING: {Config.PAPER_TRADING}")
