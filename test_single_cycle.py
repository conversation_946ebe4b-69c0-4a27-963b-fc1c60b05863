#!/usr/bin/env python3
"""
Test script to run a single trading cycle and verify trade execution
"""

import logging
from datetime import datetime
from main import TradingApp, TradingEngine
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_trading_cycle():
    """Test a single trading cycle to verify everything works"""
    
    print("=== Single Trading Cycle Test ===")
    print(f"Time: {datetime.now()}")
    print(f"Trading thresholds: BUY > {Config.BUY_THRESHOLD}, SELL < {Config.SELL_THRESHOLD}")
    print()
    
    try:
        # Initialize the trading app
        print("1. Initializing trading app...")
        app = TradingApp()
        print("✅ Trading app initialized successfully")
        
        # Get trading symbols
        print("\n2. Getting trading symbols...")
        symbols = app.get_trading_symbols()
        print(f"✅ Got {len(symbols)} symbols: {symbols}")
        
        # Test one trading cycle
        print("\n3. Running single trading cycle...")
        
        # Fetch market data
        print("   - Fetching market data...")
        market_data = app.data_ingestor.get_market_data(symbols)
        print(f"   ✅ Market data for {len(market_data)} symbols")
        
        # Fetch news data
        print("   - Fetching news data...")
        news_data = app.data_ingestor.get_news_data(symbols)
        print(f"   ✅ News data fetched")
        
        # Get sector data
        print("   - Fetching sector data...")
        try:
            from sector_performance import get_sector_performance
            sector_data = get_sector_performance()
            print(f"   ✅ Sector data: {len(sector_data)} sectors")
        except Exception as e:
            print(f"   ⚠️  Sector data failed: {e}")
            sector_data = {}
        
        # Run analysis
        print("   - Running analysis...")
        results = app.trading_engine.run_trading_cycle(symbols, market_data, news_data, sector_data)
        print(f"   ✅ Analysis complete: {len(results)} results")
        
        # Show analysis results
        print("\n4. Analysis Results:")
        buy_signals = []
        sell_signals = []
        hold_signals = []
        
        for result in results:
            symbol = result['symbol']
            recommendation = result['recommendation']
            signal = result['combined_signal']
            
            print(f"   {symbol}: {recommendation} (signal: {signal:.3f})")
            
            if recommendation == 'BUY':
                buy_signals.append(result)
            elif recommendation == 'SELL':
                sell_signals.append(result)
            else:
                hold_signals.append(result)
        
        print(f"\n   Summary: {len(buy_signals)} BUY, {len(sell_signals)} SELL, {len(hold_signals)} HOLD")
        
        # Test trade execution
        print("\n5. Testing trade execution...")
        if results:
            executed_trades = app.trading_engine.execute_trades(results)
            print(f"✅ Trade execution complete: {len(executed_trades)} trades executed")
            
            if executed_trades:
                print("   Executed trades:")
                for trade in executed_trades:
                    print(f"   - {trade.get('side', 'N/A')} {trade.get('qty', 'N/A')} {trade.get('symbol', 'N/A')}")
            else:
                print("   No trades were executed (this is normal if signals don't meet thresholds)")
                
                # Show why no trades were executed
                print("\n   Reasons no trades were executed:")
                for result in results:
                    symbol = result['symbol']
                    recommendation = result['recommendation']
                    signal_strength = abs(result['combined_signal'])
                    
                    if recommendation == 'BUY':
                        if signal_strength <= 0.02:
                            print(f"   - {symbol}: BUY signal too weak ({signal_strength:.3f} <= 0.02)")
                    elif recommendation == 'SELL':
                        print(f"   - {symbol}: SELL signal but no position exists")
        else:
            print("❌ No analysis results to execute")
        
        # Check account status
        print("\n6. Account Status:")
        account_info = app.portfolio.get_account_info()
        if account_info:
            print(f"   Buying Power: ${account_info.get('buying_power', 0):,.2f}")
            print(f"   Portfolio Value: ${account_info.get('portfolio_value', 0):,.2f}")
            print(f"   Cash: ${account_info.get('cash', 0):,.2f}")
        
        print("\n🎉 Single trading cycle test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error in trading cycle test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_single_trading_cycle()
    
    if success:
        print("\n✅ Your trading app is working correctly!")
        print("You can now run 'python main.py' to start continuous trading.")
    else:
        print("\n❌ There are still issues to fix.")
        print("Please check the error messages above.")
