import pandas as pd
import time
import sys

def get_sp500_symbols():
    """Get list of S&P 500 symbols from Wikipedia"""
    print("Starting SP500 symbol retrieval...")
    try:
        # Fetch from Wikipedia
        print("Fetching tables from Wikipedia...")
        start_time = time.time()
        tables = pd.read_html("https://en.wikipedia.org/wiki/List_of_S%26P_500_companies")
        fetch_time = time.time() - start_time
        print(f"Fetched {len(tables)} tables in {fetch_time:.2f} seconds")
        
        # Process the first table
        print(f"Processing table with shape: {tables[0].shape}")
        print(f"Table columns: {tables[0].columns.tolist()}")
        
        # Extract symbols
        df = tables[0]
        if "Symbol" not in df.columns:
            print(f"WARNING: 'Symbol' column not found. Available columns: {df.columns.tolist()}")
            # Try to find a similar column
            for col in df.columns:
                if "symbol" in col.lower() or "ticker" in col.lower():
                    print(f"Using '{col}' column instead of 'Symbol'")
                    df["Symbol"] = df[col]
                    break
        
        # Extract and clean symbols
        symbols = df["Symbol"].str.strip().tolist()
        print(f"Extracted {len(symbols)} symbols")
        
        # Show some sample data
        print("\nSample data from the table:")
        print(df.head(3).to_string())
        
        return symbols
    except Exception as e:
        print(f"ERROR fetching S&P 500 symbols: {e}")
        print(f"Exception type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return []

# Test SP500 symbols retrieval
print(f"Python version: {sys.version}")
print(f"Pandas version: {pd.__version__}")
print("-" * 50)

symbols = get_sp500_symbols()
print("-" * 50)
print(f"Retrieved {len(symbols)} SP500 symbols")
if symbols:
    print(f"First 10 symbols: {symbols[:10]}")
    print(f"Last 10 symbols: {symbols[-10:]}")
else:
    print("Failed to retrieve SP500 symbols")

# Check for potential issues in the symbols
if symbols:
    print("\nChecking for potential issues in symbols...")
    # Check for empty strings
    empty_symbols = [i for i, s in enumerate(symbols) if not s]
    if empty_symbols:
        print(f"WARNING: Found {len(empty_symbols)} empty symbols at indices: {empty_symbols}")
    
    # Check for duplicates
    duplicates = set([s for s in symbols if symbols.count(s) > 1])
    if duplicates:
        print(f"WARNING: Found {len(duplicates)} duplicate symbols: {duplicates}")
    
    # Check for unusual characters
    unusual = [s for s in symbols if not s.isalnum()]
    if unusual:
        print(f"WARNING: Found {len(unusual)} symbols with unusual characters: {unusual}")




