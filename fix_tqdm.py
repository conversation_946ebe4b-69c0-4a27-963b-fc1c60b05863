import subprocess
import sys
import os
import shutil
import site

def run_command(cmd):
    print(f"Running: {cmd}")
    subprocess.run(cmd, shell=True, check=True)

print("Fixing tqdm package issue...")

# 1. First, uninstall tqdm completely
run_command(f"{sys.executable} -m pip uninstall -y tqdm")

# 2. Install a specific version of tqdm known to work
run_command(f"{sys.executable} -m pip install tqdm==4.65.0")

# 3. Find the correct site-packages directory
site_packages_dirs = site.getsitepackages()
venv_site_packages = None

# First check if we're in a virtual environment
if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
    venv_site_packages = os.path.join(sys.prefix, 'Lib', 'site-packages')
    if os.path.exists(venv_site_packages):
        site_packages_dirs = [venv_site_packages] + site_packages_dirs

# Find tqdm directory in site-packages
tqdm_path = None
for site_dir in site_packages_dirs:
    potential_tqdm_path = os.path.join(site_dir, "tqdm")
    if os.path.exists(potential_tqdm_path):
        tqdm_path = potential_tqdm_path
        print(f"Found tqdm package at: {tqdm_path}")
        break

if not tqdm_path:
    print("Could not find tqdm package directory. Trying alternative approach...")
    # Try to find it using pip show
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "show", "tqdm"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        for line in result.stdout.splitlines():
            if line.startswith("Location:"):
                location = line.split(":", 1)[1].strip()
                tqdm_path = os.path.join(location, "tqdm")
                if os.path.exists(tqdm_path):
                    print(f"Found tqdm package at: {tqdm_path}")
                    break
    except Exception as e:
        print(f"Error finding tqdm with pip show: {e}")

if not tqdm_path:
    print("ERROR: Could not find tqdm package directory.")
    sys.exit(1)

# 4. Check if the utils.py file exists in the tqdm package
utils_path = os.path.join(tqdm_path, "utils.py")

if not os.path.exists(utils_path):
    print(f"Creating missing utils.py file in {tqdm_path}")
    
    # Create a basic utils.py file with required functions
    with open(utils_path, "w") as f:
        f.write("""
# Minimal tqdm.utils module to fix dependency issues
import os
import re
import sys
import time
from warnings import warn

# Simple CLS (clear screen) cross-platform implementation
def _term_move_up():
    return '\x1b[A'

def _screen_shape_wrapper():
    \"\"\"
    Return a function which returns console dimensions (width, height).
    \"\"\"
    _screen_shape = None
    try:
        import shutil
        _screen_shape = shutil.get_terminal_size
    except (ImportError, AttributeError):
        pass
    
    if _screen_shape is None:
        def _screen_shape(*_):
            return (80, 24)
    
    return _screen_shape

def _environ_cols_wrapper():
    \"\"\"
    Return a function which returns console width.
    \"\"\"
    return lambda: _screen_shape_wrapper()()[0]

def _is_utf(encoding):
    return encoding.lower().startswith('utf-') or ('U8' == encoding)

def _supports_unicode(file):
    return getattr(file, 'encoding', None) and _is_utf(file.encoding)

def _is_ascii(s):
    if isinstance(s, str):
        for c in s:
            if ord(c) > 127:
                return False
        return True
    return _supports_unicode(s)

def _is_notebook():
    \"\"\"
    Check if we're running in a Jupyter Notebook
    \"\"\"
    try:
        get_ipython = sys.modules['IPython'].get_ipython
        if 'IPKernelApp' in get_ipython().config:
            return True
    except (ImportError, AttributeError, KeyError):
        pass
    return False

def _is_colab():
    \"\"\"
    Check if we're running in Google Colab
    \"\"\"
    try:
        import google.colab
        return True
    except ImportError:
        return False

def _is_kaggle():
    \"\"\"
    Check if we're running in Kaggle Notebook
    \"\"\"
    return os.environ.get('KAGGLE_KERNEL_RUN_TYPE', '') != ''

# Additional functions needed by tqdm
def disp_len(data):
    \"\"\"
    Returns the real on-screen length of a string which may contain
    ANSI control codes and wide chars.
    \"\"\"
    return len(re.sub('\x1b\\[\\d+m', '', data))

def disp_trim(data, length):
    \"\"\"
    Trim a string which may contain ANSI control characters.
    \"\"\"
    if len(data) <= length:
        return data
    return data[:length]
""")

    print(f"Created utils.py file at: {utils_path}")
else:
    print(f"utils.py already exists at: {utils_path}")

# 5. Also check for _utils.py which might be needed
utils_path = os.path.join(tqdm_path, "_utils.py")
if not os.path.exists(utils_path):
    print(f"Creating missing _utils.py file in {tqdm_path}")
    
    with open(utils_path, "w") as f:
        f.write("""
from warnings import warn

from .std import TqdmDeprecationWarning
from .utils import (  # NOQA, pylint: disable=unused-import
    _environ_cols_wrapper, _is_ascii, _is_utf, _screen_shape_wrapper, 
    _supports_unicode, _term_move_up)

warn("This function will be removed in tqdm==5.0.0\\n"
     "Please use `tqdm.utils.*` instead of `tqdm._utils.*`",
     TqdmDeprecationWarning, stacklevel=2)
""")
    
    print(f"Created _utils.py file at: {utils_path}")
else:
    print(f"_utils.py already exists at: {utils_path}")

print("\nTqdm package has been fixed. Try running your main.py again.")
