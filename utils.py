"""
Utility functions for stock trading application.
"""
import pandas as pd
import json
import time
import os
import logging
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/utils.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
SP500_CACHE_FILE = "sp500_symbols.json"
CACHE_EXPIRY = 86400  # 24 hours in seconds

def get_sp500_symbols(use_cache: bool = True, cache_expiry: int = CACHE_EXPIRY) -> List[str]:
    """
    Get list of S&P 500 symbols from Wikipedia with caching support.
    
    Args:
        use_cache: Whether to use cached data if available
        cache_expiry: Cache expiry time in seconds (default: 24 hours)
        
    Returns:
        List of S&P 500 stock symbols
    """
    # Check cache first if enabled
    if use_cache and os.path.exists(SP500_CACHE_FILE):
        try:
            with open(SP500_CACHE_FILE, "r") as f:
                data = json.load(f)
                # Check if cache is still valid
                if time.time() - data.get("timestamp", 0) < cache_expiry:
                    logger.info(f"Using cached S&P 500 symbols (count: {len(data['symbols'])})")
                    return data["symbols"]
                else:
                    logger.info("Cache expired, fetching fresh S&P 500 symbols")
        except Exception as e:
            logger.warning(f"Error reading cache file: {e}")
    
    # Fetch from Wikipedia
    try:
        logger.info("Fetching S&P 500 symbols from Wikipedia")
        tables = pd.read_html("https://en.wikipedia.org/wiki/List_of_S%26P_500_companies")
        df = tables[0]
        symbols = df["Symbol"].str.strip().tolist()
        
        # Save to cache
        save_sp500_symbols(symbols)
        
        logger.info(f"Successfully fetched {len(symbols)} S&P 500 symbols")
        return symbols
    except Exception as e:
        logger.error(f"Error fetching S&P 500 symbols: {e}")
        
        # If cache exists but was expired, use it as fallback
        if os.path.exists(SP500_CACHE_FILE):
            try:
                with open(SP500_CACHE_FILE, "r") as f:
                    data = json.load(f)
                    logger.warning(f"Using expired cache as fallback (count: {len(data['symbols'])})")
                    return data["symbols"]
            except Exception as cache_error:
                logger.error(f"Error reading fallback cache: {cache_error}")
        
        return []

def save_sp500_symbols(symbols: List[str]) -> None:
    """
    Save S&P 500 symbols to a cache file.
    
    Args:
        symbols: List of S&P 500 stock symbols
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(SP500_CACHE_FILE) or '.', exist_ok=True)
        
        with open(SP500_CACHE_FILE, "w") as f:
            json.dump({"symbols": symbols, "timestamp": time.time()}, f)
        logger.info(f"Saved {len(symbols)} S&P 500 symbols to cache")
    except Exception as e:
        logger.error(f"Error saving S&P 500 symbols to cache: {e}")

def get_sp500_symbols_with_sectors() -> Dict[str, str]:
    """
    Get dictionary mapping S&P 500 symbols to their sectors.
    
    Returns:
        Dictionary mapping stock symbols to their sectors
    """
    try:
        # Fetch from Wikipedia
        tables = pd.read_html("https://en.wikipedia.org/wiki/List_of_S%26P_500_companies")
        df = tables[0]
        
        # Create dictionary mapping symbols to sectors
        sectors = {}
        for _, row in df.iterrows():
            symbol = row["Symbol"].strip()
            sector = row["GICS Sector"].strip()
            sectors[symbol] = sector
        
        logger.info(f"Successfully fetched sectors for {len(sectors)} S&P 500 symbols")
        return sectors
    except Exception as e:
        logger.error(f"Error fetching S&P 500 sectors: {e}")
        return {}

def get_sp500_symbols_with_details() -> List[Dict[str, Any]]:
    """
    Get detailed information about S&P 500 companies.
    
    Returns:
        List of dictionaries with company details
    """
    try:
        # Fetch from Wikipedia
        tables = pd.read_html("https://en.wikipedia.org/wiki/List_of_S%26P_500_companies")
        df = tables[0]
        
        # Convert to list of dictionaries
        companies = []
        for _, row in df.iterrows():
            company = {
                "symbol": row["Symbol"].strip(),
                "name": row["Security"].strip(),
                "sector": row["GICS Sector"].strip(),
                "sub_industry": row["GICS Sub-Industry"].strip(),
                "headquarters": row["Headquarters Location"].strip(),
                "date_added": row["Date added"].strip() if pd.notna(row["Date added"]) else None,
                "cik": row["CIK"].strip() if pd.notna(row["CIK"]) else None,
                "founded": row["Founded"].strip() if pd.notna(row["Founded"]) else None
            }
            companies.append(company)
        
        logger.info(f"Successfully fetched details for {len(companies)} S&P 500 companies")
        return companies
    except Exception as e:
        logger.error(f"Error fetching S&P 500 company details: {e}")
        return []

def format_currency(value: Union[float, int]) -> str:
    """
    Format a value as currency.
    
    Args:
        value: Numeric value to format
        
    Returns:
        Formatted currency string
    """
    return f"${value:,.2f}"

def format_percentage(value: Union[float, int], include_sign: bool = True) -> str:
    """
    Format a value as percentage.
    
    Args:
        value: Numeric value to format (0.01 = 1%)
        include_sign: Whether to include the % sign
        
    Returns:
        Formatted percentage string
    """
    if include_sign:
        return f"{value:.2f}%"
    return f"{value:.2f}"

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split a list into chunks of specified size.
    
    Args:
        lst: List to split
        chunk_size: Size of each chunk
        
    Returns:
        List of list chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def sanitize_symbol(symbol: str) -> str:
    """
    Sanitize a stock symbol for use in filenames or as keys.
    
    Args:
        symbol: Stock symbol
        
    Returns:
        Sanitized symbol
    """
    return symbol.replace('.', '_').replace('-', '_').replace('/', '_')

def is_market_open() -> bool:
    """
    Check if the US stock market is currently open.
    
    Returns:
        True if market is open, False otherwise
    """
    now = datetime.now()
    
    # Check if it's a weekday (0 = Monday, 4 = Friday)
    if now.weekday() > 4:
        return False
    
    # Check if it's between 9:30 AM and 4:00 PM Eastern Time
    # Note: This is a simplified check and doesn't account for holidays
    eastern_time = now - timedelta(hours=5)  # Rough EST conversion
    market_open = eastern_time.replace(hour=9, minute=30, second=0)
    market_close = eastern_time.replace(hour=16, minute=0, second=0)
    
    return market_open <= eastern_time <= market_close

def get_trading_days(start_date: str, end_date: str) -> List[str]:
    """
    Get a list of trading days between start_date and end_date.
    
    Args:
        start_date: Start date in 'YYYY-MM-DD' format
        end_date: End date in 'YYYY-MM-DD' format
        
    Returns:
        List of trading days in 'YYYY-MM-DD' format
    """
    try:
        # Create date range
        date_range = pd.date_range(start=start_date, end=end_date)
        
        # Filter out weekends
        trading_days = [d.strftime('%Y-%m-%d') for d in date_range if d.weekday() < 5]
        
        return trading_days
    except Exception as e:
        logger.error(f"Error getting trading days: {e}")
        return []

def calculate_moving_average(data: List[float], window: int) -> List[float]:
    """
    Calculate moving average for a list of values.
    
    Args:
        data: List of numeric values
        window: Window size for moving average
        
    Returns:
        List of moving averages (same length as input, with NaN for first window-1 elements)
    """
    if not data:
        return []
    
    result = []
    for i in range(len(data)):
        if i < window - 1:
            result.append(float('nan'))
        else:
            window_avg = sum(data[i-window+1:i+1]) / window
            result.append(window_avg)
    
    return result

def safe_request(url: str, timeout: int = 10, max_retries: int = 3) -> Optional[requests.Response]:
    """
    Make a safe HTTP request with retries.
    
    Args:
        url: URL to request
        timeout: Request timeout in seconds
        max_retries: Maximum number of retry attempts
        
    Returns:
        Response object or None if failed
    """
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            logger.warning(f"Request failed (attempt {attempt+1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                logger.error(f"All {max_retries} request attempts failed for URL: {url}")
                return None
            time.sleep(1)  # Wait before retrying

def create_directory_if_not_exists(directory: str) -> None:
    """
    Create a directory if it doesn't exist.
    
    Args:
        directory: Directory path
    """
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Created directory: {directory}")




