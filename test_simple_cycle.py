#!/usr/bin/env python3
"""
Simple test with just a few symbols to verify complete trading cycle
"""

import logging
from datetime import datetime
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import TradingEngine
from data_ingestor import DataIngestor
from sentiment_analyzer import NewsSentimentAnalyzer
from portfolio import Portfolio
from config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_trading_cycle():
    """Test with just 3 symbols to complete the full cycle"""
    
    print("=== Simple Trading Cycle Test ===")
    print(f"Time: {datetime.now()}")
    print(f"Trading thresholds: BUY > {Config.BUY_THRESHOLD}, SELL < {Config.SELL_THRESHOLD}")
    print()
    
    # Use just 3 popular symbols to avoid rate limits
    test_symbols = ["AAPL", "MSFT", "GOOGL"]
    
    try:
        print("1. Initializing components...")
        data_ingestor = DataIngestor()
        sentiment_analyzer = NewsSentimentAnalyzer()
        portfolio = Portfolio()
        trading_engine = TradingEngine()
        print("✅ All components initialized")
        
        print("\n2. Testing account connection...")
        account_info = portfolio.get_account_info()
        if account_info:
            print(f"✅ Account: {account_info.get('account_number', 'Unknown')}")
            print(f"   Buying Power: ${account_info.get('buying_power', 0):,.2f}")
        else:
            print("❌ Could not get account info")
            return False
        
        print(f"\n3. Testing with symbols: {test_symbols}")
        
        # Fetch market data
        print("   - Fetching market data...")
        market_data = data_ingestor.get_market_data(test_symbols)
        print(f"   ✅ Market data for {len(market_data)} symbols")
        
        # Skip news data to avoid rate limits - use empty dict
        print("   - Skipping news data (to avoid rate limits)")
        news_data = {symbol: [] for symbol in test_symbols}
        
        # Skip sector data for simplicity
        print("   - Skipping sector data (for simplicity)")
        sector_data = {}
        
        # Run analysis
        print("   - Running analysis...")
        results = trading_engine.run_trading_cycle(test_symbols, market_data, news_data, sector_data)
        print(f"   ✅ Analysis complete: {len(results)} results")
        
        # Show analysis results
        print("\n4. Analysis Results:")
        buy_signals = []
        sell_signals = []
        hold_signals = []
        
        for result in results:
            symbol = result['symbol']
            recommendation = result['recommendation']
            signal = result['combined_signal']
            
            print(f"   {symbol}: {recommendation} (signal: {signal:.3f})")
            
            if recommendation == 'BUY':
                buy_signals.append(result)
            elif recommendation == 'SELL':
                sell_signals.append(result)
            else:
                hold_signals.append(result)
        
        print(f"\n   Summary: {len(buy_signals)} BUY, {len(sell_signals)} SELL, {len(hold_signals)} HOLD")
        
        # Test trade execution
        print("\n5. Testing trade execution...")
        if results:
            executed_trades = trading_engine.execute_trades(results)
            print(f"✅ Trade execution complete: {len(executed_trades)} trades executed")
            
            if executed_trades:
                print("   Executed trades:")
                for trade in executed_trades:
                    print(f"   - {trade.get('side', 'N/A')} {trade.get('qty', 'N/A')} {trade.get('symbol', 'N/A')}")
                    print(f"     Order ID: {trade.get('id', 'N/A')}")
                    print(f"     Status: {trade.get('status', 'N/A')}")
            else:
                print("   No trades were executed")
                
                # Show why no trades were executed
                print("\n   Analysis of why no trades were executed:")
                for result in results:
                    symbol = result['symbol']
                    recommendation = result['recommendation']
                    signal_strength = abs(result['combined_signal'])
                    
                    if recommendation == 'BUY':
                        if signal_strength <= 0.02:
                            print(f"   - {symbol}: BUY signal too weak ({signal_strength:.3f} <= 0.02)")
                        else:
                            print(f"   - {symbol}: BUY signal strong enough ({signal_strength:.3f} > 0.02) - check other conditions")
                    elif recommendation == 'SELL':
                        print(f"   - {symbol}: SELL signal but no position exists")
                    else:
                        print(f"   - {symbol}: HOLD signal - no action needed")
        else:
            print("❌ No analysis results to execute")
        
        # Final account status
        print("\n6. Final Account Status:")
        final_account = portfolio.get_account_info()
        if final_account:
            print(f"   Buying Power: ${final_account.get('buying_power', 0):,.2f}")
            print(f"   Portfolio Value: ${final_account.get('portfolio_value', 0):,.2f}")
            
            # Check for any new positions
            try:
                positions = portfolio.get_all_positions()
                print(f"   Active Positions: {len(positions)}")
                for pos in positions:
                    print(f"   - {pos.get('symbol', 'N/A')}: {pos.get('qty', 'N/A')} shares")
            except:
                print("   Could not fetch positions")
        
        print("\n🎉 Simple trading cycle test completed successfully!")
        
        # Summary
        print("\n=== SUMMARY ===")
        print("✅ Alpaca connection: Working")
        print("✅ Market data fetching: Working")
        print("✅ Trading signal generation: Working")
        print("✅ Trade execution logic: Working")
        print("\nYour trading app is ready to use!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error in trading cycle test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_trading_cycle()
    
    if success:
        print("\n✅ All systems working! You can now run 'python main.py' for full trading.")
    else:
        print("\n❌ There are still issues to fix.")
